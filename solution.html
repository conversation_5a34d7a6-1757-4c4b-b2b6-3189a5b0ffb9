<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阿里云CDN + GA加速 + 自建证书平台解决方案</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-12">
        <div class="container mx-auto px-6 text-center">
            <h1 class="text-4xl font-bold mb-4">
                <i class="fas fa-cloud mr-3"></i>
                阿里云CDN + GA加速 + 自建证书平台解决方案
            </h1>
            <p class="text-xl opacity-90">替代Cloudflare企业版，年节省$42,000</p>
        </div>
    </header>

    <main class="container mx-auto px-6 py-12">
        <!-- 方案架构 -->
        <section class="mb-16">
            <h2 class="text-3xl font-bold text-center mb-8">方案架构</h2>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-xl shadow-lg text-center">
                    <i class="fas fa-cloud text-4xl text-blue-600 mb-4"></i>
                    <h3 class="text-xl font-bold mb-3">阿里云CDN</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 静态资源分发</li>
                        <li>• 路由规则配置</li>
                        <li>• 全球节点覆盖</li>
                        <li>• 月流量20TB支持</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg text-center">
                    <i class="fas fa-globe text-4xl text-green-600 mb-4"></i>
                    <h3 class="text-xl font-bold mb-3">全球加速GA</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 动态内容加速</li>
                        <li>• 智能路由优化</li>
                        <li>• 网络质量提升</li>
                        <li>• API请求加速</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg text-center">
                    <i class="fas fa-certificate text-4xl text-purple-600 mb-4"></i>
                    <h3 class="text-xl font-bold mb-3">自建证书平台</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• SSL自动签发</li>
                        <li>• 300商户域名支持</li>
                        <li>• 证书生命周期管理</li>
                        <li>• 自动续期部署</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 成本对比 -->
        <section class="mb-16">
            <h2 class="text-3xl font-bold text-center mb-8">成本对比分析</h2>
            <div class="grid md:grid-cols-2 gap-8">
                <div class="bg-red-50 p-8 rounded-xl border-l-4 border-red-500">
                    <h3 class="text-2xl font-bold text-red-800 mb-4">当前Cloudflare企业版</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between text-lg">
                            <span>月费用</span>
                            <span class="font-bold text-red-600">$4,000</span>
                        </div>
                        <div class="flex justify-between text-lg">
                            <span>年费用</span>
                            <span class="font-bold text-red-600">$48,000</span>
                        </div>
                    </div>
                </div>
                <div class="bg-green-50 p-8 rounded-xl border-l-4 border-green-500">
                    <h3 class="text-2xl font-bold text-green-800 mb-4">新方案成本</h3>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between">
                            <span>阿里云CDN (20TB)</span>
                            <span>¥2,000</span>
                        </div>
                        <div class="flex justify-between">
                            <span>全球加速GA</span>
                            <span>¥1,500</span>
                        </div>
                        <div class="flex justify-between">
                            <span>证书平台适配</span>
                            <span>内部开发 (不计成本)</span>
                        </div>
                        <div class="border-t pt-2 flex justify-between text-lg font-bold">
                            <span>月运营成本</span>
                            <span class="text-green-600">¥3,500 (~$500)</span>
                        </div>
                    </div>
                    <div class="bg-green-100 p-3 rounded text-center">
                        <span class="text-green-800 font-bold text-xl">节省87.5%成本</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 当前CF配置分析 -->
        <section class="mb-16">
            <h2 class="text-3xl font-bold text-center mb-8">当前Cloudflare配置分析</h2>
            <div class="bg-white rounded-xl p-8 shadow-lg">
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-bold mb-4 text-blue-600">Worker路由配置</h3>
                        <div class="space-y-3 text-sm">
                            <div class="bg-gray-50 p-3 rounded">
                                <strong>static.fbtools.top/ip.js</strong><br>
                                <span class="text-gray-600">返回客户端真实IP</span>
                            </div>
                            <div class="bg-gray-50 p-3 rounded">
                                <strong>*/sa.gif → collect.powerbuyin.top</strong><br>
                                <span class="text-gray-600">数据收集跟踪</span>
                            </div>
                            <div class="bg-gray-50 p-3 rounded">
                                <strong>*/buyer-front/* → 静态资源</strong><br>
                                <span class="text-gray-600">前端资源分发</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-4 text-green-600">页面规则配置</h3>
                        <div class="space-y-3 text-sm">
                            <div class="bg-gray-50 p-3 rounded">
                                <strong>*/fb-report* → datasink.powerbuyin.top</strong><br>
                                <span class="text-gray-600">Facebook报告转发</span>
                            </div>
                            <div class="bg-gray-50 p-3 rounded">
                                <strong>/buyer/user/user-capi → /fb-report</strong><br>
                                <span class="text-gray-600">Facebook CAPI重定向</span>
                            </div>
                            <div class="bg-gray-50 p-3 rounded">
                                <strong>apple-developer-merchantid</strong><br>
                                <span class="text-gray-600">苹果支付验证</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-6 p-4 bg-yellow-50 rounded-lg">
                    <h4 class="font-bold text-yellow-800 mb-2">迁移注意事项</h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• 广告域名(adorado.top, magic.powershopy.com)不迁移，由北京团队处理</li>
                        <li>• 不考虑图片压缩优化功能，重点关注路由适配</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 迁移计划 -->
        <section class="mb-16">
            <h2 class="text-3xl font-bold text-center mb-8">迁移实施计划 (2025.7.28 - 2025.8.18)</h2>
            <div class="bg-red-50 p-4 rounded-lg mb-6 text-center">
                <span class="text-red-800 font-bold text-lg">
                    <i class="fas fa-clock mr-2"></i>
                    紧急项目：仅21天完成所有迁移
                </span>
            </div>
            <div class="space-y-6">
                <!-- 第一阶段 -->
                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center mr-4">1</div>
                        <h3 class="text-xl font-bold">测试环境开发验证 (7.28-8.3, 7天)</h3>
                    </div>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold mb-2">代码适配</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• 适配现有证书平台代码</li>
                                <li>• 配置Let's Encrypt接口</li>
                                <li>• 证书管理后台优化</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">路由适配</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• /sa.gif → collect.powerbuyin.top</li>
                                <li>• /buyer-front/* → 静态资源</li>
                                <li>• /fb-report 重定向</li>
                                <li>• ip.js 返回客户端IP</li>
                            </ul>
                        </div>
                    </div>
                    <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2">测试环境</h4>
                        <p class="text-sm text-blue-600">使用 powerbuyin.top 域名进行所有功能验证和性能测试</p>
                    </div>
                </div>

                <!-- 第二阶段 -->
                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center mr-4">2</div>
                        <h3 class="text-xl font-bold">生产环境迁移 (8.4-8.10, 7天)</h3>
                    </div>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold mb-2">CDN域名 (~10个)</h4>
                            <p class="text-sm text-gray-600">img.fbtools.top, static.fbtools.top 等</p>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-2">平台域名 (~10个)</h4>
                            <p class="text-sm text-gray-600">powershopy.com, powerbuyin.top 等</p>
                        </div>
                    </div>
                </div>

                <!-- 第三阶段 -->
                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center mr-4">3</div>
                        <h3 class="text-xl font-bold">商户域名分批迁移 (8.11-8.18, 7天)</h3>
                    </div>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="text-center p-3 bg-purple-50 rounded">
                            <div class="font-bold text-purple-800">10个</div>
                            <div class="text-sm text-purple-600">8.11-8.12</div>
                        </div>
                        <div class="text-center p-3 bg-purple-50 rounded">
                            <div class="font-bold text-purple-800">100个</div>
                            <div class="text-sm text-purple-600">8.13-8.15</div>
                        </div>
                        <div class="text-center p-3 bg-purple-50 rounded">
                            <div class="font-bold text-purple-800">剩余所有</div>
                            <div class="text-sm text-purple-600">8.16-8.18</div>
                        </div>
                    </div>
                    <div class="mt-4 p-3 bg-purple-100 rounded">
                        <p class="text-sm text-purple-700">
                            300商户域名，每个2个证书（主域名+www），总计600个证书，部署到Nginx服务器
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 证书管理说明 -->
        <section class="mb-16">
            <h2 class="text-3xl font-bold text-center mb-8">证书管理说明</h2>
            <div class="grid md:grid-cols-2 gap-8">
                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <h3 class="text-xl font-bold mb-4 text-blue-600">平台域名证书</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• powershopy.com, powerbuyin.top 等</li>
                        <li>• 单独申请，不通过自建平台</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <h3 class="text-xl font-bold mb-4 text-purple-600">商户域名证书</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 300个客户自定义域名</li>
                        <li>• 自建平台自动签发管理</li>
                        <li>• Let's Encrypt，到期前7天续期</li>
                        <li>• 部署到Nginx服务器</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 技术实现细节 -->
        <section class="mb-16">
            <h2 class="text-3xl font-bold text-center mb-8">技术实现细节</h2>
            <div class="grid md:grid-cols-2 gap-8">
                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <h3 class="text-xl font-bold mb-4 text-blue-600">
                        <i class="fas fa-code mr-2"></i>
                        路由适配实现
                    </h3>
                    <div class="space-y-4">
                        <div class="border-l-4 border-blue-500 pl-4">
                            <h4 class="font-semibold">/sa.gif 路由</h4>
                            <p class="text-sm text-gray-600">任意域名的/sa.gif请求转发到 collect.powerbuyin.top</p>
                            <code class="text-xs bg-gray-100 p-1 rounded">*/sa.gif → collect.powerbuyin.top</code>
                        </div>
                        <div class="border-l-4 border-green-500 pl-4">
                            <h4 class="font-semibold">/buyer-front/* 路由</h4>
                            <p class="text-sm text-gray-600">任意域名的/buyer-front/请求转发到静态资源</p>
                            <code class="text-xs bg-gray-100 p-1 rounded">*/buyer-front/* → buyer-front-fat</code>
                        </div>
                        <div class="border-l-4 border-purple-500 pl-4">
                            <h4 class="font-semibold">/fb-report 重定向</h4>
                            <p class="text-sm text-gray-600">/buyer/user/user-capi 重定向到 /fb-report</p>
                            <code class="text-xs bg-gray-100 p-1 rounded">/buyer/user/user-capi → /fb-report</code>
                        </div>
                        <div class="border-l-4 border-orange-500 pl-4">
                            <h4 class="font-semibold">ip.js 实现</h4>
                            <p class="text-sm text-gray-600">返回客户端真实IPv4地址（暂不支持IPv6）</p>
                            <code class="text-xs bg-gray-100 p-1 rounded">static.fbtools.top/ip.js</code>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <h3 class="text-xl font-bold mb-4 text-green-600">
                        <i class="fas fa-certificate mr-2"></i>
                        商户证书管理实现
                    </h3>
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">证书签发流程</h4>
                            <ol class="text-sm text-gray-600 space-y-1">
                                <li>1. 商户域名验证（HTTP-01/DNS-01）</li>
                                <li>2. Let's Encrypt ACME v2 API调用</li>
                                <li>3. 证书生成和存储</li>
                                <li>4. 自动部署到Nginx服务器</li>
                            </ol>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">证书续期策略</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• 到期前7天自动检测</li>
                                <li>• 自动签发新证书</li>
                                <li>• 自动更新Nginx配置</li>
                                <li>• 证书透明度日志监控</li>
                            </ul>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold mb-2">批量管理策略</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• 使用现有重试机制</li>
                                <li>• 多账户避免Let's Encrypt限流</li>
                                <li>• 并发签发控制</li>
                                <li>• 失败告警和监控</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 风险与应对 -->
        <section class="mb-16">
            <h2 class="text-3xl font-bold text-center mb-8">风险与应对</h2>
            <div class="grid md:grid-cols-2 gap-8">
                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <h3 class="text-xl font-bold mb-4 text-red-600">主要风险</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 21天时间紧迫</li>
                        <li>• Let's Encrypt限流风险</li>
                        <li>• DNS切换服务中断</li>
                        <li>• 功能兼容性问题</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg">
                    <h3 class="text-xl font-bold mb-4 text-green-600">应对策略</h3>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li>• 现有代码复用快速开发</li>
                        <li>• 多账户 + 预签发策略</li>
                        <li>• 分批迁移 + 快速回滚</li>
                        <li>• 并行开发 + 充分测试</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 总结 -->
        <section>
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl p-8 text-center">
                <h2 class="text-3xl font-bold mb-6">方案总结</h2>
                <div class="grid md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white bg-opacity-20 rounded-lg p-4">
                        <i class="fas fa-dollar-sign text-2xl mb-2"></i>
                        <div class="font-bold">成本节省87.5%</div>
                        <div class="text-sm opacity-90">年节省$42,000</div>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg p-4">
                        <i class="fas fa-certificate text-2xl mb-2"></i>
                        <div class="font-bold">600个证书</div>
                        <div class="text-sm opacity-90">300商户域名</div>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg p-4">
                        <i class="fas fa-clock text-2xl mb-2"></i>
                        <div class="font-bold">21天完成</div>
                        <div class="text-sm opacity-90">2025.8.18截止</div>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg p-4">
                        <i class="fas fa-code text-2xl mb-2"></i>
                        <div class="font-bold">代码复用</div>
                        <div class="text-sm opacity-90">快速实施</div>
                    </div>
                </div>
                <p class="text-lg opacity-90 max-w-4xl mx-auto">
                    基于现有代码基础，通过阿里云CDN + GA加速 + 自建证书平台的组合方案，
                    在21天内完成320个域名的迁移。大幅降低运营成本的同时获得更强的自主控制能力。
                </p>
            </div>
        </section>
    </main>

    <footer class="bg-gray-800 text-white py-8 mt-16">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-400">
                <i class="fas fa-calendar-alt mr-2"></i>
                方案制定：2025.7.28 | 
                <i class="fas fa-clock mr-2"></i>
                项目截止：2025.8.18
            </p>
        </div>
    </footer>
</body>
</html>