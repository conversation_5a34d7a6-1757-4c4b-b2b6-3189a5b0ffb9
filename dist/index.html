<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档索引 - 网页导航</title>
    <!-- TailwindCSS 通过CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        // 配置Tailwind主题
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                    },
                }
            }
        }
    </script>
    <style type="text/css">
        /* 自定义样式 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }

        /* 加载动画 */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 顶部导航栏 -->
    <header class="sticky top-0 z-50 bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fas fa-book-open text-primary-600 dark:text-primary-400 text-2xl"></i>
                <h1 class="text-xl font-bold">文档索引中心</h1>
            </div>
            <div class="flex items-center space-x-4">
                <!-- 深色/浅色模式切换按钮 -->
                <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-moon hidden dark:block text-yellow-300"></i>
                    <i class="fas fa-sun block dark:hidden text-yellow-500"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-4 py-8 fade-in">
        <!-- 介绍部分 -->
        <section class="mb-12 text-center">
            <h2 class="text-3xl font-bold mb-4">欢迎访问文档索引</h2>
            <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                这里收集了所有可用的文档和指南，帮助您快速找到所需的信息。
            </p>
        </section>

        <!-- 文档卡片网格 -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Windsurf指南 -->
            <a href="windsurf-guide.html" class="card-hover group block bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-blue-100 dark:bg-blue-900 p-3 rounded-full">
                            <i class="fas fa-wind text-blue-600 dark:text-blue-300"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">指南</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">Windsurf 指南</h3>
                    <p class="text-gray-600 dark:text-gray-400">全面了解Windsurf的使用方法和功能介绍。</p>
                </div>
                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">查看详情</span>
                    <i class="fas fa-arrow-right text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <!-- Windsurf最佳实践 -->
            <a href="windsurf-best-practices.html" class="card-hover group block bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-green-100 dark:bg-green-900 p-3 rounded-full">
                            <i class="fas fa-check-double text-green-600 dark:text-green-300"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">最佳实践</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">Windsurf 最佳实践</h3>
                    <p class="text-gray-600 dark:text-gray-400">提高效率的技巧和最佳实践指南。</p>
                </div>
                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">查看详情</span>
                    <i class="fas fa-arrow-right text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <!-- Cursor指南 -->
            <a href="cursor-guide.html" class="card-hover group block bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-purple-100 dark:bg-purple-900 p-3 rounded-full">
                            <i class="fas fa-terminal text-purple-600 dark:text-purple-300"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">指南</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">Cursor 指南</h3>
                    <p class="text-gray-600 dark:text-gray-400">Cursor工具的详细使用说明和功能介绍。</p>
                </div>
                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">查看详情</span>
                    <i class="fas fa-arrow-right text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <!-- Cursor小程序教程 -->
            <a href="cursor-xiaochengxu-tutorial.html" class="card-hover group block bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-indigo-100 dark:bg-indigo-900 p-3 rounded-full">
                            <i class="fas fa-mobile-alt text-indigo-600 dark:text-indigo-300"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">教程</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">Cursor 小程序教程</h3>
                    <p class="text-gray-600 dark:text-gray-400">学习如何使用Cursor开发小程序的详细教程。</p>
                </div>
                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">查看详情</span>
                    <i class="fas fa-arrow-right text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <!-- 21天SaaS -->
            <a href="21days-saas.html" class="card-hover group block bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-red-100 dark:bg-red-900 p-3 rounded-full">
                            <i class="fas fa-rocket text-red-600 dark:text-red-300"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">教程</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">21天 SaaS 指南</h3>
                    <p class="text-gray-600 dark:text-gray-400">21天内构建SaaS产品的完整指南。</p>
                </div>
                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">查看详情</span>
                    <i class="fas fa-arrow-right text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <!-- Web模板分析 -->
            <a href="web-templates-analysis.html" class="card-hover group block bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-orange-100 dark:bg-orange-900 p-3 rounded-full">
                            <i class="fas fa-code text-orange-600 dark:text-orange-300"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">分析</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">Web模板分析</h3>
                    <p class="text-gray-600 dark:text-gray-400">当前流行的Web开发框架与模板的详细分析。</p>
                </div>
                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">查看详情</span>
                    <i class="fas fa-arrow-right text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <!-- 移动应用设计工作流 -->
            <a href="mobile-app-design-workflow.html" class="card-hover group block bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-purple-100 dark:bg-purple-900 p-3 rounded-full">
                            <i class="fas fa-mobile-screen-button text-purple-600 dark:text-purple-300"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">工作流</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">移动应用设计工作流</h3>
                    <p class="text-gray-600 dark:text-gray-400">Prajwal Tomar的10步移动应用设计与开发流程。</p>
                </div>
                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">查看详情</span>
                    <i class="fas fa-arrow-right text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <!-- 移动应用设计工作流 -->
            <a href="mobile-app-design.html" class="card-hover group block bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-orange-100 dark:bg-orange-900 p-3 rounded-full">
                            <i class="fas fa-mobile-screen-button text-orange-600 dark:text-orange-300"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">设计</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">移动应用设计工作流</h3>
                    <p class="text-gray-600 dark:text-gray-400">无需Figma的客户端应用开发设计指南。</p>
                </div>
                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">查看详情</span>
                    <i class="fas fa-arrow-right text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <!-- Shopify Shop App 竞品调研报告 -->
            <a href="shopify-shop-app-analysis.html" class="card-hover group block bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-gradient-to-br from-blue-100 to-orange-100 dark:from-blue-900 dark:to-orange-900 p-3 rounded-full">
                            <i class="fas fa-chart-line text-blue-600 dark:text-blue-300"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">竞品分析</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">Shopify Shop App 竞品调研</h3>
                    <p class="text-gray-600 dark:text-gray-400">深度分析Shopify Shop App产品策略与商业模式，为H5购物门户提供战略指导。</p>
                </div>
                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">查看详情</span>
                    <i class="fas fa-arrow-right text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <!-- 广州龙舟指南 -->
            <a href="guangzhou-dragon-boat-guide-2025.html" class="card-hover group block bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-gradient-to-br from-green-100 to-yellow-100 dark:from-green-900 dark:to-yellow-900 p-3 rounded-full">
                            <i class="fas fa-dragon text-green-600 dark:text-green-300"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">文化指南</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">广州龙舟观赛指南 2025</h3>
                    <p class="text-gray-600 dark:text-gray-400">2025年广州端午龙舟赛事完整观赛指南，传统文化与现代体验的完美结合。</p>
                </div>
                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">查看详情</span>
                    <i class="fas fa-arrow-right text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <!-- 白云山家庭指南 -->
            <a href="baiyunshan-family-guide.html" class="card-hover group block bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="bg-gradient-to-br from-green-100 to-blue-100 dark:from-green-900 dark:to-blue-900 p-3 rounded-full">
                            <i class="fas fa-mountain text-green-600 dark:text-green-300"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">旅游指南</span>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">白云山家庭游玩指南</h3>
                    <p class="text-gray-600 dark:text-gray-400">广州白云山完整的家庭游玩攻略，包含路线规划、景点介绍和实用贴士。</p>
                </div>
                <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">查看详情</span>
                    <i class="fas fa-arrow-right text-primary-600 dark:text-primary-400 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white dark:bg-gray-800 shadow-inner mt-12 py-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <p class="text-gray-600 dark:text-gray-400">&copy; 2025 文档索引中心. 保留所有权利。</p>
                </div>
                <div class="flex space-x-4">
                    <a href="https://github.com/caiwenhao" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                        <i class="fab fa-github text-xl"></i>
                    </a>
                    <a href="https://twitter.com/caiwenhao" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                        <i class="fab fa-twitter text-xl"></i>
                    </a>
                    <a href="https://linkedin.com/in/caiwenhao" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                        <i class="fab fa-linkedin text-xl"></i>
                    </a>
                </div>
            </div>
            <div class="mt-4 text-center">
                <p class="text-sm text-gray-500 dark:text-gray-500">作者：蔡文浩</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 深色/浅色模式切换
        const themeToggle = document.getElementById('theme-toggle');
        const html = document.documentElement;

        // 检查系统偏好
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            html.classList.add('dark');
        }

        // 检查本地存储中的主题设置
        if (localStorage.getItem('theme') === 'dark') {
            html.classList.add('dark');
        } else if (localStorage.getItem('theme') === 'light') {
            html.classList.remove('dark');
        }

        // 主题切换按钮点击事件
        themeToggle.addEventListener('click', () => {
            if (html.classList.contains('dark')) {
                html.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }
        });

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', () => {
            document.querySelectorAll('.fade-in').forEach(element => {
                element.style.opacity = '0';
                setTimeout(() => {
                    element.style.opacity = '1';
                }, 100);
            });
        });
    </script>
</body>
</html>
