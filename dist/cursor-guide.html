<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor免费无限期使用全攻略</title>
    <!-- TailwindCSS 3.0+ -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- 自定义配置 -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/css">
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: transparent;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 4px;
        }
        
        .dark ::-webkit-scrollbar-thumb {
            background: #4a5568;
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 文章内容格式 */
        .article-content h2 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a202c;
        }
        
        .dark .article-content h2 {
            color: #f7fafc;
        }
        
        .article-content h3 {
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
            font-size: 1.25rem;
            font-weight: 600;
            color: #1a202c;
        }
        
        .dark .article-content h3 {
            color: #f7fafc;
        }
        
        .article-content p {
            margin-bottom: 1rem;
            line-height: 1.7;
        }
        
        .article-content ul {
            margin-bottom: 1rem;
            padding-left: 1rem;
            list-style-type: disc;
        }
        
        .article-content ol {
            margin-bottom: 1rem;
            padding-left: 1rem;
            list-style-type: decimal;
        }
        
        .article-content li {
            margin-bottom: 0.5rem;
            line-height: 1.7;
        }
    </style>
</head>
<body class="antialiased bg-gray-50 text-gray-800 dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 深色模式切换 -->
    <script>
        // 检查系统偏好设置
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.body.classList.add('dark');
        }
        
        // 切换深色/浅色模式
        function toggleDarkMode() {
            document.body.classList.toggle('dark');
            
            // 保存用户偏好设置
            if (document.body.classList.contains('dark')) {
                localStorage.setItem('darkMode', 'true');
            } else {
                localStorage.setItem('darkMode', 'false');
            }
        }
        
        // 读取用户偏好设置
        document.addEventListener('DOMContentLoaded', () => {
            const darkModePref = localStorage.getItem('darkMode');
            if (darkModePref === 'true') {
                document.body.classList.add('dark');
            } else if (darkModePref === 'false') {
                document.body.classList.remove('dark');
            }
        });
    </script>

    <div class="min-h-screen flex flex-col">
        <!-- 导航栏 -->
        <header class="sticky top-0 z-10 backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border-b border-gray-200 dark:border-gray-700 shadow-sm">
            <div class="container mx-auto px-4 py-4 flex items-center justify-between">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary-600 dark:text-primary-400 mr-2" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20.71,4.04C20.37,3.7 19.84,3.62 19.43,3.9L18,4.95L19.06,5.69L20.11,4.93C20.36,4.76 20.54,4.43 20.54,4.15C20.54,4.11 20.53,4.06 20.71,4.04M9.12,19.93L8.05,19.2L16.82,6.97L17.89,7.7L9.12,19.93Z"></path>
                        <path d="M19.07,6.73L19.13,6.77L19.2,6.84L19.07,6.73M7.07,18.89L3,20.96V17.93L7.07,18.89M5,16.04V15.45L15,2.63C15.19,2.4 15.47,2.27 15.76,2.27C16.5,2.27 17.09,2.86 17.09,3.6C17.09,3.89 16.96,4.18 16.73,4.37L7.07,17.08V15.88L7.06,15.88L15.82,4.37L16.89,5.11L8.11,17.34L7.06,17.03V17.08L7.07,18.89Z"></path>
                    </svg>
                    <h1 class="text-xl font-semibold">Cursor免费使用攻略</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="toggleDarkMode()" class="rounded-full p-2 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                        <span class="material-icons hidden dark:block">light_mode</span>
                        <span class="material-icons block dark:hidden">dark_mode</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主内容 -->
        <main class="flex-grow container mx-auto px-4 py-8 md:py-12 lg:px-8 flex flex-col lg:flex-row">
            <!-- 侧边目录 -->
            <aside class="hidden lg:block lg:w-64 sticky top-24 self-start overflow-y-auto pr-4 space-y-2">
                <h3 class="text-lg font-semibold mb-4">目录</h3>
                <nav class="space-y-2">
                    <a href="#intro" class="block py-1 px-4 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">简介</a>
                    <a href="#pricing" class="block py-1 px-4 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">官方定价与免费计划</a>
                    <a href="#official-methods" class="block py-1 px-4 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">官方合法免费使用方式</a>
                    <a href="#unofficial-methods" class="block py-1 px-4 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">非官方使用方法与风险</a>
                    <a href="#alternatives" class="block py-1 px-4 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">免费替代品对比</a>
                    <a href="#conclusion" class="block py-1 px-4 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">结论</a>
                </nav>
            </aside>

            <!-- 文章内容 -->
            <article class="flex-grow lg:ml-8">
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-md p-6 md:p-8 animate-fade-in article-content">
                    <div class="mb-6 flex flex-col md:flex-row md:items-center justify-between">
                        <h1 id="intro" class="text-2xl md:text-3xl font-bold mb-2 md:mb-0">Cursor免费无限期使用全攻略：方法、风险与替代方案</h1>
                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <span class="material-icons text-sm mr-1">calendar_today</span>
                            <time>2025年2月</time>
                        </div>
                    </div>

                    <div class="prose prose-lg max-w-none dark:prose-invert">
                        <p class="text-lg">
                            Cursor作为一款功能强大的AI代码编辑器，因其内置GPT-4和Claude 3.5等高级模型而广受开发者欢迎。虽然其完整功能需要付费订阅，但本文将全面介绍官方提供的免费计划、合法延长免费试用的方法，以及一些可能存在风险的非官方方法，最后比较几款功能相近的免费替代品，帮助开发者在经济与功能之间做出平衡选择。
                        </p>

                        <!-- 定价部分 -->
                        <section id="pricing" class="mt-10">
                            <h2 class="group flex items-center">
                                <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">attach_money</span>
                                Cursor官方定价与免费计划
                                <a href="#pricing" class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <span class="material-icons text-gray-400 dark:text-gray-500 text-sm">link</span>
                                </a>
                            </h2>
                            <p>Cursor提供三种不同的使用计划，满足不同用户的需求：</p>

                            <h3 class="group flex items-center">
                                <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">card_giftcard</span>
                                Hobby免费计划
                                <a href="#hobby" class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <span class="material-icons text-gray-400 dark:text-gray-500 text-sm">link</span>
                                </a>
                            </h3>
                            
                            <div class="bg-gray-50 dark:bg-gray-800/70 rounded-xl p-5 my-4 border border-gray-200 dark:border-gray-700 transform hover:scale-[1.01] transition-transform">
                                <p>Cursor的免费版本（Hobby计划）提供以下功能和限制：</p>
                                <ul class="mt-3 space-y-2">
                                    <li class="flex items-start">
                                        <span class="material-icons text-green-500 mr-2 mt-0.5">check_circle</span>
                                        <span>Pro版本两周免费试用期</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="material-icons text-green-500 mr-2 mt-0.5">check_circle</span>
                                        <span>2000次代码补全额度</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="material-icons text-green-500 mr-2 mt-0.5">check_circle</span>
                                        <span>50次慢速高级请求（如使用GPT-4、GPT-4o和Claude 3.5/3.7 Sonnet等模型）</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="material-icons text-green-500 mr-2 mt-0.5">check_circle</span>
                                        <span>200次cursor-small模型使用</span>
                                    </li>
                                </ul>
                                <p class="mt-3 text-gray-600 dark:text-gray-400">这些限制对于初学者或想要尝试Cursor功能的开发者来说已经足够用于基础体验。</p>
                            </div>

                            <h3 class="group flex items-center">
                                <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">paid</span>
                                付费计划概览
                                <a href="#paid" class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <span class="material-icons text-gray-400 dark:text-gray-500 text-sm">link</span>
                                </a>
                            </h3>

                            <div class="grid md:grid-cols-2 gap-4 my-4">
                                <!-- Pro计划卡片 -->
                                <div class="bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-center mb-4">
                                        <h4 class="text-lg font-semibold">Pro计划</h4>
                                        <div class="text-primary-600 dark:text-primary-400 font-bold">$20/月</div>
                                    </div>
                                    <ul class="space-y-2">
                                        <li class="flex items-start text-sm">
                                            <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                            <span>无限代码补全</span>
                                        </li>
                                        <li class="flex items-start text-sm">
                                            <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                            <span>每月500次快速高级请求</span>
                                        </li>
                                        <li class="flex items-start text-sm">
                                            <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                            <span>无限慢速高级请求</span>
                                        </li>
                                    </ul>
                                </div>
                                
                                <!-- Business计划卡片 -->
                                <div class="bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-center mb-4">
                                        <h4 class="text-lg font-semibold">Business计划</h4>
                                        <div class="text-primary-600 dark:text-primary-400 font-bold">$40/用户/月</div>
                                    </div>
                                    <ul class="space-y-2">
                                        <li class="flex items-start text-sm">
                                            <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                            <span>Pro计划的所有功能</span>
                                        </li>
                                        <li class="flex items-start text-sm">
                                            <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                            <span>组织级隐私模式</span>
                                        </li>
                                        <li class="flex items-start text-sm">
                                            <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                            <span>集中结算和管理员面板</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </section>

                        <!-- 官方合法免费使用方式 -->
                        <section id="official-methods" class="mt-10">
                            <h2 class="group flex items-center">
                                <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">verified</span>
                                官方合法免费使用方式
                                <a href="#official-methods" class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <span class="material-icons text-gray-400 dark:text-gray-500 text-sm">link</span>
                                </a>
                            </h2>
                            <p>如果你想以官方支持的方式免费使用Cursor，有以下几种选择：</p>

                            <div class="grid md:grid-cols-3 gap-6 my-6">
                                <!-- 学生教育计划 -->
                                <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300">
                                    <div class="bg-blue-500 text-white p-4">
                                        <div class="flex items-center">
                                            <span class="material-icons mr-2">school</span>
                                            <h3 class="font-semibold">学生教育计划</h3>
                                        </div>
                                    </div>
                                    <div class="p-5">
                                        <p class="text-sm mb-4">作为GitHub学生开发者包（GitHub Student Developer Pack）的一部分，学生可以免费使用Cursor。</p>
                                        
                                        <h4 class="font-semibold text-sm mb-2">申请步骤：</h4>
                                        <ol class="list-decimal list-inside text-sm space-y-2">
                                            <li>访问GitHub教育网站（education.github.com/pack）</li>
                                            <li>使用学校教育邮箱或提交学生证明文件申请学生认证</li>
                                            <li>认证成功后即可免费使用Cursor的完整功能</li>
                                        </ol>
                                    </div>
                                </div>

                                <!-- 邀请好友获得积分 -->
                                <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300">
                                    <div class="bg-green-500 text-white p-4">
                                        <div class="flex items-center">
                                            <span class="material-icons mr-2">people</span>
                                            <h3 class="font-semibold">邀请好友获得积分</h3>
                                        </div>
                                    </div>
                                    <div class="p-5">
                                        <p class="text-sm mb-4">Cursor提供邀请奖励计划，通过邀请朋友注册使用Cursor可以获得积分：</p>
                                        
                                        <ul class="list-disc list-inside text-sm space-y-2">
                                            <li>每成功邀请一位朋友注册并使用Cursor，你会获得一定积分</li>
                                            <li>这些积分可以用来兑换更多服务或延长免费使用时间</li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- 关注官方活动 -->
                                <div class="bg-white dark:bg-gray-800 rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300">
                                    <div class="bg-purple-500 text-white p-4">
                                        <div class="flex items-center">
                                            <span class="material-icons mr-2">campaign</span>
                                            <h3 class="font-semibold">关注官方活动</h3>
                                        </div>
                                    </div>
                                    <div class="p-5">
                                        <p class="text-sm mb-4">Cursor不定期会推出特别促销活动，提供额外的免费使用机会。建议：</p>
                                        
                                        <ul class="list-disc list-inside text-sm space-y-2">
                                            <li>关注Cursor官方网站和社交媒体账号</li>
                                            <li>订阅Cursor的电子邮件通讯</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!-- 非官方使用方法与风险 -->
                        <section id="unofficial-methods" class="mt-10">
                            <h2 class="group flex items-center">
                                <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">warning</span>
                                非官方使用方法与风险
                                <a href="#unofficial-methods" class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <span class="material-icons text-gray-400 dark:text-gray-500 text-sm">link</span>
                                </a>
                            </h2>
                            <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 rounded-lg p-4 my-4">
                                <div class="flex items-start">
                                    <span class="material-icons text-amber-500 mr-2 mt-0.5">info</span>
                                    <p class="text-amber-800 dark:text-amber-200 text-sm">以下方法可能违反Cursor的服务条款，使用前请充分了解相关风险。</p>
                                </div>
                            </div>

                            <div class="space-y-8 mt-6">
                                <!-- 邮箱别名技巧 -->
                                <div class="rounded-xl overflow-hidden shadow-sm bg-white dark:bg-gray-800 hover:shadow-md transition-shadow">
                                    <div class="border-b border-gray-200 dark:border-gray-700 p-5">
                                        <h3 class="font-semibold flex items-center">
                                            <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">alternate_email</span>
                                            邮箱别名技巧
                                        </h3>
                                    </div>
                                    <div class="p-5">
                                        <p class="mb-4">多个搜索结果介绍了使用邮箱别名来创建多个账号的方法：</p>
                                        
                                        <div class="space-y-6">
                                            <!-- Gmail别名法 -->
                                            <div>
                                                <h4 class="font-semibold text-base mb-2">Gmail别名法：</h4>
                                                <ul class="list-disc list-inside space-y-2 pl-2">
                                                    <li>在Gmail地址中添加"+"号和任意字符（例如：<EMAIL>）</li>
                                                    <li>验证邮件仍会发送到原始邮箱，但Cursor会将其识别为不同账号</li>
                                                </ul>
                                                <div class="bg-gray-100 dark:bg-gray-700 rounded p-3 mt-2 text-sm font-mono">
                                                    原始: <EMAIL><br>
                                                    变体1: user<span class="text-green-600 dark:text-green-400">+cursor1</span>@gmail.com<br>
                                                    变体2: user<span class="text-green-600 dark:text-green-400">+pro2023</span>@gmail.com
                                                </div>
                                            </div>
                                            
                                            <!-- 2925邮箱法 -->
                                            <div>
                                                <h4 class="font-semibold text-base mb-2">2925邮箱法：</h4>
                                                <ul class="list-disc list-inside space-y-2 pl-2">
                                                    <li>注册一个2925.com的邮箱（如******************）</li>
                                                    <li>通过添加"+"号和任意字符创建无限变体（如ai_coding<EMAIL>）</li>
                                                    <li>验证码会发送到原始邮箱，无需多次注册</li>
                                                </ul>
                                                <div class="bg-gray-100 dark:bg-gray-700 rounded p-3 mt-2 text-sm font-mono">
                                                    原始: <EMAIL><br>
                                                    变体1: ai_coding<span class="text-green-600 dark:text-green-400">+free1</span>@2925.com<br>
                                                    变体2: ai_coding<span class="text-green-600 dark:text-green-400">+test42</span>@2925.com
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 修改设备机器码 -->
                                <div class="rounded-xl overflow-hidden shadow-sm bg-white dark:bg-gray-800 hover:shadow-md transition-shadow">
                                    <div class="border-b border-gray-200 dark:border-gray-700 p-5">
                                        <h3 class="font-semibold flex items-center">
                                            <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">developer_mode</span>
                                            修改设备机器码
                                        </h3>
                                    </div>
                                    <div class="p-5">
                                        <p class="mb-4">当用户多次使用免费试用账号时，Cursor会记录设备机器码并限制同一设备创建更多免费试用账号。解决方法：</p>
                                        
                                        <div class="space-y-6">
                                            <!-- 使用专用工具 -->
                                            <div>
                                                <h4 class="font-semibold text-base mb-2">使用专用工具：</h4>
                                                <ul class="list-disc list-inside space-y-2 pl-2">
                                                    <li>下载"cursor-fake-machine"等工具</li>
                                                    <li>安装插件并使用命令重置设备ID</li>
                                                </ul>
                                                <div class="bg-gray-100 dark:bg-gray-700 rounded p-3 mt-2 text-sm font-mono overflow-x-auto">
                                                    $ npm install -g cursor-fake-machine<br>
                                                    $ cursor-fake-machine reset
                                                </div>
                                            </div>
                                            
                                            <!-- 手动修改 -->
                                            <div>
                                                <h4 class="font-semibold text-base mb-2">手动修改：</h4>
                                                <ul class="list-disc list-inside space-y-2 pl-2">
                                                    <li>找到并修改storage.json文件中的设备标识符</li>
                                                </ul>
                                                <div class="bg-gray-100 dark:bg-gray-700 rounded p-3 mt-2 text-sm">
                                                    <p class="font-semibold mb-1">文件位置因操作系统而异：</p>
                                                    <p class="font-mono mb-1">Windows: %APPDATA%\Cursor\User\globalStorage\storage.json</p>
                                                    <p class="font-mono mb-1">MacOS: ~/Library/Application Support/Cursor/User/globalStorage/storage.json</p>
                                                    <p class="font-mono">Linux: ~/.config/Cursor/User/globalStorage/storage.json</p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">截至2025年2月，这些方法仍然有效，但官方可能随时修复这些漏洞。</p>
                                    </div>
                                </div>

                                <!-- 风险与注意事项 -->
                                <div class="rounded-xl overflow-hidden shadow-sm bg-white dark:bg-gray-800 hover:shadow-md transition-shadow">
                                    <div class="border-b border-gray-200 dark:border-gray-700 p-5">
                                        <h3 class="font-semibold flex items-center">
                                            <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">gpp_maybe</span>
                                            风险与注意事项
                                        </h3>
                                    </div>
                                    <div class="p-5">
                                        <p class="mb-4">使用非官方方法可能带来以下风险：</p>
                                        
                                        <ul class="space-y-3">
                                            <li class="flex items-start">
                                                <span class="material-icons text-red-500 mr-2 mt-0.5">error</span>
                                                <span>违反Cursor的服务条款，账号可能被封禁</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-red-500 mr-2 mt-0.5">error</span>
                                                <span>软件稳定性可能受到影响</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-red-500 mr-2 mt-0.5">error</span>
                                                <span>官方随时可能修复这些漏洞</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-red-500 mr-2 mt-0.5">error</span>
                                                <span>无法获得官方支持和服务保障</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!-- 免费替代品对比 -->
                        <section id="alternatives" class="mt-10">
                            <h2 class="group flex items-center">
                                <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">compare</span>
                                免费替代品对比
                                <a href="#alternatives" class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <span class="material-icons text-gray-400 dark:text-gray-500 text-sm">link</span>
                                </a>
                            </h2>
                            <p>如果你不想使用上述方法，可以考虑以下几款功能相近的免费替代品：</p>

                            <!-- 国产AI编程工具 -->
                            <h3 class="mt-6 mb-4 font-semibold flex items-center">
                                <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">public</span>
                                国产AI编程工具
                            </h3>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                                <!-- Trae卡片 -->
                                <div class="flex flex-col h-full bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow overflow-hidden border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                                        <h4 class="font-semibold">Trae（字节跳动）</h4>
                                        <div class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">完全免费</div>
                                    </div>
                                    <div class="flex-grow p-4 space-y-3">
                                        <ul class="space-y-2 text-sm">
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>完全免费使用，集成Claude 3.7和GPT-4o等主流AI模型（国内版为豆包大模型和DeepSeek模型）</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>支持原生中文和多种编程语言</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="mt-auto p-4 pt-0">
                                        <a href="#" class="text-primary-600 dark:text-primary-400 hover:underline text-sm flex items-center">
                                            <span class="material-icons text-sm mr-1">open_in_new</span>
                                            了解更多
                                        </a>
                                    </div>
                                </div>

                                <!-- 通义灵码卡片 -->
                                <div class="flex flex-col h-full bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow overflow-hidden border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                                        <h4 class="font-semibold">通义灵码（阿里云）</h4>
                                        <div class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">完全免费</div>
                                    </div>
                                    <div class="flex-grow p-4 space-y-3">
                                        <ul class="space-y-2 text-sm">
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>基于通义大模型的智能编码辅助工具</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>提供行级/函数级实时续写、自然语言生成代码、单元测试生成等功能</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>特别针对阿里云服务使用场景进行优化</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="mt-auto p-4 pt-0">
                                        <a href="#" class="text-primary-600 dark:text-primary-400 hover:underline text-sm flex items-center">
                                            <span class="material-icons text-sm mr-1">open_in_new</span>
                                            了解更多
                                        </a>
                                    </div>
                                </div>

                                <!-- CodeGeeX卡片 -->
                                <div class="flex flex-col h-full bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow overflow-hidden border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                                        <h4 class="font-semibold">CodeGeeX（智谱AI）</h4>
                                        <div class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">开源</div>
                                    </div>
                                    <div class="flex-grow p-4 space-y-3">
                                        <ul class="space-y-2 text-sm">
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>基于130亿参数预训练大模型的开源AI编程助手</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>支持代码自动生成和补全、代码翻译、自动添加注释等功能</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>对个人用户完全免费，代码模型已开源</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="mt-auto p-4 pt-0">
                                        <a href="#" class="text-primary-600 dark:text-primary-400 hover:underline text-sm flex items-center">
                                            <span class="material-icons text-sm mr-1">open_in_new</span>
                                            了解更多
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- 国际开源AI工具 -->
                            <h3 class="mt-6 mb-4 font-semibold flex items-center">
                                <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">language</span>
                                国际开源AI工具
                            </h3>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <!-- Codeium卡片 -->
                                <div class="flex flex-col h-full bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow overflow-hidden border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                                        <h4 class="font-semibold">Codeium</h4>
                                        <div class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">个人免费</div>
                                    </div>
                                    <div class="flex-grow p-4 space-y-3">
                                        <ul class="space-y-2 text-sm">
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>个人版完全免费的AI代码助手</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>支持70多种编程语言和40多个编辑器</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>提供代码补全、重构提示和代码解释功能</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="mt-auto p-4 pt-0">
                                        <a href="#" class="text-primary-600 dark:text-primary-400 hover:underline text-sm flex items-center">
                                            <span class="material-icons text-sm mr-1">open_in_new</span>
                                            了解更多
                                        </a>
                                    </div>
                                </div>

                                <!-- Amazon CodeWhisperer卡片 -->
                                <div class="flex flex-col h-full bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow overflow-hidden border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                                        <h4 class="font-semibold">Amazon CodeWhisperer</h4>
                                        <div class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">个人免费</div>
                                    </div>
                                    <div class="flex-grow p-4 space-y-3">
                                        <ul class="space-y-2 text-sm">
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>亚马逊研发的免费AI代码生成工具</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>对个人用户提供无限制的代码智能生成服务</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>通过安装VSCode插件"AWS Toolkit"使用</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="mt-auto p-4 pt-0">
                                        <a href="#" class="text-primary-600 dark:text-primary-400 hover:underline text-sm flex items-center">
                                            <span class="material-icons text-sm mr-1">open_in_new</span>
                                            了解更多
                                        </a>
                                    </div>
                                </div>

                                <!-- TabNine卡片 -->
                                <div class="flex flex-col h-full bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow overflow-hidden border border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                                        <h4 class="font-semibold">TabNine</h4>
                                        <div class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">基础版免费</div>
                                    </div>
                                    <div class="flex-grow p-4 space-y-3">
                                        <ul class="space-y-2 text-sm">
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>提供AI驱动的代码补全，与多种代码编辑器集成</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>既有免费版本也有付费版本</span>
                                            </li>
                                            <li class="flex items-start">
                                                <span class="material-icons text-green-500 mr-2 text-sm mt-0.5">check_circle</span>
                                                <span>支持广泛的编程语言</span>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="mt-auto p-4 pt-0">
                                        <a href="#" class="text-primary-600 dark:text-primary-400 hover:underline text-sm flex items-center">
                                            <span class="material-icons text-sm mr-1">open_in_new</span>
                                            了解更多
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!-- 结论 -->
                        <section id="conclusion" class="mt-10">
                            <h2 class="group flex items-center">
                                <span class="material-icons text-primary-600 dark:text-primary-400 mr-2">summarize</span>
                                结论
                                <a href="#conclusion" class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <span class="material-icons text-gray-400 dark:text-gray-500 text-sm">link</span>
                                </a>
                            </h2>
                            
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 my-6 border border-blue-100 dark:border-blue-800/30">
                                <p class="mb-4">对于想要免费无限期使用Cursor的开发者，有几种路径可以选择。最推荐的是通过官方合法渠道，如学生教育计划或邀请好友获取额外使用时间。这些方法不仅合规，还能获得官方支持。</p>
                                
                                <p class="mb-4">如果你愿意接受一定风险，非官方方法如邮箱别名和修改机器码也是可行选择，但需要注意这些方法可能随时失效，且存在违反服务条款的风险。</p>
                                
                                <p>对于长期使用，建议考虑以下选择：</p>
                                <ol class="list-decimal list-inside space-y-2 mt-3">
                                    <li>如果经济条件允许，订阅Cursor Pro版获得完整体验（$20/月）</li>
                                    <li>如果是学生，通过GitHub学生开发者包免费使用</li>
                                    <li>尝试功能相近的免费替代品，如Trae、CodeGeeX或Codeium等</li>
                                </ol>
                                
                                <p class="mt-4">最终，选择哪种方式取决于你的具体需求、技术水平和对风险的接受程度。无论选择哪种方式，这些AI编程工具都能显著提升开发效率，帮助你更高效地完成编程任务。</p>
                            </div>
                        </section>
                    </div>
                </div>
            </article>
        </main>

        <!-- 页脚 -->
        <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-8 mt-12">
            <div class="container mx-auto px-4">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="mb-6 md:mb-0">
                        <h3 class="text-lg font-semibold mb-2">作者信息</h3>
                        <p class="text-gray-600 dark:text-gray-400">文浩 Cai</p>
                    </div>
                    
                    <div class="flex space-x-4">
                        <a href="https://github.com/wenhaotan" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                        <a href="https://twitter.com/wenhaotan" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"></path>
                            </svg>
                        </a>
                        <a href="https://www.linkedin.com/in/wenhaotan" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <div class="mt-8 text-center text-sm text-gray-500 dark:text-gray-400">
                    <p>© 2025 版权所有</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- 滚动到顶部按钮 -->
    <button id="back-to-top" class="fixed bottom-6 right-6 p-2 rounded-full bg-primary-600 text-white shadow-lg opacity-0 invisible transition-all z-50 hover:bg-primary-700">
        <span class="material-icons">arrow_upward</span>
    </button>

    <script>
        // 监听滚动事件，显示/隐藏回到顶部按钮
        document.addEventListener('DOMContentLoaded', function() {
            const backToTopButton = document.getElementById('back-to-top');
            
            // 点击回到顶部
            backToTopButton.addEventListener('click', function() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
            
            // 监听滚动
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.remove('opacity-100', 'visible');
                    backToTopButton.classList.add('opacity-0', 'invisible');
                }
            });
            
            // 为主要内容区块添加淡入动画
            const contentSections = document.querySelectorAll('section');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('opacity-100');
                        entry.target.classList.remove('opacity-0', 'translate-y-4');
                    }
                });
            }, { threshold: 0.1 });
            
            contentSections.forEach(section => {
                section.classList.add('transition-all', 'duration-700', 'opacity-0', 'translate-y-4');
                observer.observe(section);
            });
        });
    </script>
</body>
</html> 