<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白云山亲子一日游完美攻略</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'bounce-gentle': 'bounceGentle 2s infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        bounceGentle: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' },
                        }
                    }
                }
            }
        }
    </script>
    <!-- Visualization & Content Choices:
        - Report Info: Ticket Prices (Table 1) -> Goal: Inform costs & child benefits -> Viz/Presentation: Chart.js Bar Chart (Adult vs Child) & HTML Table -> Interaction: Chart tooltips -> Justification: Visually emphasizes cost savings for child, clear price listing -> Library/Method: Chart.js, HTML/Tailwind.
        - Report Info: Recommended Itinerary (Table 2) -> Goal: Provide clear, step-by-step daily plan -> Viz/Presentation: Interactive HTML timeline/cards with icons -> Interaction: Expand/collapse details for each step (simplified to static display for brevity in this version, but designed for future interaction) -> Justification: Easy to follow, highlights key details per activity, kid-friendly icons -> Library/Method: HTML/Tailwind, Vanilla JS (for future interaction).
        - Report Info: Packing List -> Goal: Aid preparation -> Viz/Presentation: Interactive HTML checklist -> Interaction: Check/uncheck items -> Justification: Practical tool for users -> Library/Method: HTML/Tailwind, Vanilla JS.
        - Report Info: Kid-friendly attractions (Mingchun Valley, Moxing Ridge Escalator, Cable Car) -> Goal: Highlight fun for the child -> Viz/Presentation: HTML cards with icons/text -> Interaction: Static display, clear callouts -> Justification: Quickly shows what the child will enjoy -> Library/Method: HTML/Tailwind.
        - Report Info: Internal Transport -> Goal: Explain options within park -> Viz/Presentation: HTML text, clear table for e-car routes/costs -> Interaction: Static display -> Justification: Clear info for navigation -> Library/Method: HTML/Tailwind.
        - CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        /* 全局样式 */
        * {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #fef7cd 0%, #fde68a 50%, #fed7aa 100%);
            min-height: 100vh;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease;
        }

        /* 导航样式 */
        .nav-link {
            padding: 0.75rem 1rem;
            color: #57534e;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover {
            color: #ea580c;
            background-color: rgba(249, 115, 22, 0.1);
            transform: translateY(-1px);
        }

        .nav-link.active {
            color: #ea580c;
            background: linear-gradient(135deg, #fed7aa, #fdba74);
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
        }

        /* 标题样式 */
        .section-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: #047857;
            margin-bottom: 2rem;
            text-align: center;
            background: linear-gradient(135deg, #047857, #059669);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 50%;
            transform: translateX(-50%);
            width: 4rem;
            height: 0.25rem;
            background: linear-gradient(90deg, #047857, #059669);
            border-radius: 0.125rem;
        }

        @media (max-width: 768px) {
            .section-title {
                font-size: 2rem;
            }
        }

        .subsection-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #059669;
            margin-bottom: 1rem;
            margin-top: 1.5rem;
        }

        /* 卡片样式 */
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 0.25rem;
            background: linear-gradient(90deg, #047857, #059669, #10b981);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .icon-emoji {
            margin-right: 0.5rem;
            display: inline-block;
            animation: bounceGentle 2s infinite;
        }

        /* 图表容器 */
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            height: 300px;
            max-height: 400px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }

        /* 清单样式 */
        .checklist-item {
            transition: all 0.2s ease;
            border-radius: 0.5rem;
            padding: 0.75rem;
        }

        .checklist-item:hover {
            background-color: rgba(251, 191, 36, 0.1);
            transform: translateX(4px);
        }

        .checklist-item input[type="checkbox"] {
            width: 1.25rem;
            height: 1.25rem;
            color: #f97316;
            border-color: #d6d3d1;
            border-radius: 0.25rem;
            margin-right: 0.75rem;
            vertical-align: middle;
            transition: all 0.2s ease;
        }

        .checklist-item input[type="checkbox"]:focus {
            outline: 2px solid #fb923c;
            outline-offset: 2px;
        }

        .checklist-item input[type="checkbox"]:checked {
            background-color: #f97316;
            border-color: #f97316;
        }

        .checklist-item label {
            color: #57534e;
            vertical-align: middle;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .checklist-item input[type="checkbox"]:checked + label {
            text-decoration: line-through;
            color: #a8a29e;
        }

        /* 选项卡样式 */
        .tab-button {
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            font-size: 0.875rem;
            line-height: 1.25;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .tab-button.active {
            background: linear-gradient(135deg, #059669, #10b981);
            color: white;
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
        }

        .tab-button:not(.active) {
            color: #059669;
            background-color: transparent;
        }

        .tab-button:not(.active):hover {
            background-color: rgba(5, 150, 105, 0.1);
            transform: translateY(-1px);
        }

        .tab-content {
            margin-top: 1rem;
            padding: 1.5rem;
            border: 1px solid rgba(5, 150, 105, 0.2);
            border-radius: 0.75rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .section-title {
                font-size: 2rem !important;
                margin-bottom: 1.5rem;
            }

            .card {
                padding: 1.5rem;
                margin-bottom: 1rem;
            }

            .nav-link {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
                margin-bottom: 0.25rem;
                border-radius: 0.5rem;
            }

            .tab-button {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
                margin-bottom: 0.5rem;
            }

            .tab-content {
                padding: 1rem;
            }

            .chart-container {
                height: 250px !important;
                padding: 0.5rem;
            }

            /* 移动端网格优化 */
            .grid {
                display: block !important;
            }

            .grid > * {
                margin-bottom: 1rem;
            }

            /* 移动端文字大小调整 */
            .subsection-title {
                font-size: 1.25rem !important;
            }

            /* 移动端按钮优化 */
            .tab-button {
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }

            /* 移动端导航按钮样式 */
            #mobile-menu-button:hover {
                background-color: rgba(5, 150, 105, 0.1);
                transform: scale(1.05);
            }
        }

        @media (max-width: 480px) {
            .section-title {
                font-size: 1.75rem !important;
            }

            .card {
                padding: 1rem;
            }

            .chart-container {
                height: 200px !important;
            }
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(5, 150, 105, 0.3);
            border-radius: 50%;
            border-top-color: #059669;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 滚动指示器 */
        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(5, 150, 105, 0.2);
            z-index: 9999;
        }

        .scroll-progress {
            height: 100%;
            background: linear-gradient(90deg, #047857, #059669, #10b981);
            width: 0%;
            transition: width 0.1s ease;
        }
    </style>
</head>
<body>
    <!-- 滚动进度指示器 -->
    <div class="scroll-indicator">
        <div class="scroll-progress" id="scrollProgress"></div>
    </div>

    <nav style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(12px); box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); position: sticky; top: 0; z-index: 50;">
        <div style="max-width: 80rem; margin: 0 auto; padding: 0 1rem;">
            <div style="display: flex; align-items: center; justify-content: space-between; height: 4rem;">
                <div style="display: flex; align-items: center;">
                    <span style="font-weight: 800; font-size: 1.5rem; background: linear-gradient(135deg, #047857, #059669); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                        🏔️ 白云山亲子游
                    </span>
                </div>
                <div style="display: none;" class="desktop-nav">
                    <div style="margin-left: 2.5rem; display: flex; align-items: baseline; gap: 0.5rem;">
                        <a href="#welcome" class="nav-link active">🏠 欢迎</a>
                        <a href="#preparation" class="nav-link">📋 行前准备</a>
                        <a href="#itinerary" class="nav-link">🗺️ 完美行程</a>
                        <a href="#kid-favorites" class="nav-link">🧸 宝贝最爱</a>
                        <a href="#tips" class="nav-link">💡 实用宝典</a>
                        <a href="#adjustments" class="nav-link">🔄 灵活调整</a>
                    </div>
                </div>
                <div class="mobile-nav-toggle">
                    <button id="mobile-menu-button" style="color: #57534e; transition: all 0.3s ease; padding: 0.5rem; border-radius: 0.5rem; background: none; border: none; cursor: pointer;">
                        <svg style="height: 1.5rem; width: 1.5rem;" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <div id="mobile-menu" style="display: none; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(12px); border-top: 1px solid rgba(5, 150, 105, 0.2);" class="mobile-nav">
            <div style="padding: 1rem; display: flex; flex-direction: column; gap: 0.5rem;">
                <a href="#welcome" class="nav-link active" style="display: block;">🏠 欢迎</a>
                <a href="#preparation" class="nav-link" style="display: block;">📋 行前准备</a>
                <a href="#itinerary" class="nav-link" style="display: block;">🗺️ 完美行程</a>
                <a href="#kid-favorites" class="nav-link" style="display: block;">🧸 宝贝最爱</a>
                <a href="#tips" class="nav-link" style="display: block;">💡 实用宝典</a>
                <a href="#adjustments" class="nav-link" style="display: block;">🔄 灵活调整</a>
            </div>
        </div>
    </nav>

    <div style="max-width: 80rem; margin: 0 auto; padding: 2rem 1rem;">

        <section id="welcome" style="margin-bottom: 4rem; scroll-margin-top: 4rem;" class="animate-fade-in">
            <div style="text-align: center; margin-bottom: 3rem;">
                <h1 class="section-title" style="font-size: 3rem; margin-bottom: 1rem; animation: slideUp 0.8s ease-out;">
                    🏔️ 白云山亲子一日游 ✨
                </h1>
                <p style="font-size: 1.25rem; color: #57534e; max-width: 600px; margin: 0 auto; line-height: 1.6; animation: slideUp 0.8s ease-out 0.2s both;">
                    为您与5岁宝贝量身打造的完美行程建议
                </p>
            </div>
            <div class="card" style="animation: slideUp 0.8s ease-out 0.4s both;">
                <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                    <span style="font-size: 2rem; margin-right: 1rem;">🌟</span>
                    <h3 style="font-size: 1.5rem; font-weight: 600; color: #059669; margin: 0;">欢迎来到白云山亲子游攻略</h3>
                </div>
                <p style="font-size: 1.125rem; color: #57534e; margin-bottom: 1.5rem; line-height: 1.7;">
                    欢迎来到这篇专为计划带5岁宝贝畅游广州白云山的家庭设计的互动攻略！白云山，被誉为“羊城第一秀”，不仅是自然的氧吧，更是充满乐趣的亲子乐园。本应用将整合众多实用信息，从详尽的行前准备，到专为小朋友定制的趣味行程，再到各种实用贴士，旨在帮助您轻松规划一次充满欢声笑语的家庭之旅。让我们一起探索如何与宝贝在白云山度过难忘的一天吧！
                </p>
                <div style="position: relative; border-radius: 1rem; overflow: hidden; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);">
                    <img src="https://placehold.co/1200x400/a7f3d0/1e3a8a?text=白云山风光旖旎"
                         alt="白云山风景"
                         style="width: 100%; height: auto; display: block; transition: transform 0.3s ease;"
                         onerror="this.src='https://placehold.co/1200x400/cccccc/333333?text=Image+Not+Found'"
                         onmouseover="this.style.transform='scale(1.05)'"
                         onmouseout="this.style.transform='scale(1)'">
                    <div style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white; padding: 1.5rem; text-align: center;">
                        <p style="margin: 0; font-weight: 500;">🌸 四季皆美的白云山等您来探索 🌸</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="preparation" class="mb-16 scroll-mt-16">
            <h2 class="section-title">行前万全准备 <span class="icon-emoji">📋</span></h2>
            <p class="text-center text-stone-600 mb-8 text-lg">古人云：“凡事预则立，不预则废。” 充足的行前准备是确保亲子游顺利愉快的关键。本部分将为您详细解析票务信息、最佳游玩时段、亲子游必备装备以及如何轻松抵达白云山，让您的旅程从一开始就井井有条，充满期待。</p>

            <div class="grid md:grid-cols-2 gap-8">
                <div class="card">
                    <h3 class="subsection-title"><span class="icon-emoji">🎟️</span>票务信息早知道</h3>
                    <p class="text-stone-600 mb-4">了解白云山各项门票价格及儿童优惠，能助您有效规划预算。5岁宝贝多数项目可享免费或优惠哦！</p>
                    <ul class="space-y-2 text-stone-700 mb-6">
                        <li><strong class="text-emerald-600">景区进山门票：</strong>成人5元。</li>
                        <li><strong class="text-emerald-600">摩星岭：</strong>成人5元。</li>
                        <li><strong class="text-emerald-600">鸣春谷：</strong>成人10元。</li>
                        <li><strong class="text-emerald-600">云台花园：</strong>成人10元。</li>
                        <li><strong class="text-emerald-600">白云索道（缆车）：</strong>上山成人25元，下山成人20元。</li>
                        <li><strong class="text-emerald-600">儿童政策亮点：</strong>
                            <ul class="list-disc list-inside ml-4 mt-1">
                                <li>索道：6周岁(含)以下或身高1.3米(含)以下儿童<strong class="text-orange-500">免票</strong>。</li>
                                <li>鸣春谷：6周岁(含)以下或身高1.2米(含)以下儿童<strong class="text-orange-500">免票</strong>。</li>
                                <li>进山门票及其他园中园：6周岁(含)以下或身高1.3米(含)以下儿童通常<strong class="text-orange-500">免费</strong> (建议现场确认)。</li>
                            </ul>
                        </li>
                    </ul>
                    <div class="chart-container">
                        <canvas id="ticketPriceChart"></canvas>
                    </div>
                    <p class="text-sm text-stone-500 mt-4">提示：儿童票价以景区当天公示为准，建议携带相关证件。</p>
                </div>

                <div class="card">
                    <h3 class="subsection-title"><span class="icon-emoji">⏰</span>最佳游玩时间与开放时段</h3>
                    <p class="text-stone-600 mb-4">选择对的时间，游玩更从容！</p>
                    <ul class="space-y-2 text-stone-700">
                        <li><strong class="text-emerald-600">景区整体开放：</strong>约 6:00/6:30 - 21:00。</li>
                        <li><strong class="text-emerald-600">云台花园：</strong>8:00 - 18:00。</li>
                        <li><strong class="text-emerald-600">鸣春谷：</strong>夏季(3月-10月) 9:00-18:00 (17:30止票)；冬季(11月-2月) 9:00-17:30 (17:00止票)。</li>
                        <li><strong class="text-emerald-600">摩星岭观光扶梯：</strong>10:00 - 19:00。</li>
                        <li><strong class="text-orange-500">亲子游最佳入园：</strong>建议早上 8:00-9:00，人少凉爽。</li>
                    </ul>
                    <p class="text-stone-600 mt-4">特别提醒：麓湖儿童乐园已于2019年停业，请勿作为游玩点。</p>
                </div>

                <div class="card md:col-span-2">
                    <h3 class="subsection-title"><span class="icon-emoji">🎒</span>出行必备：亲子游装备清单 (可勾选)</h3>
                    <p class="text-stone-600 mb-4">带齐装备，旅途无忧！点击勾选已准备好的物品吧。</p>
                    <div id="packingList" class="space-y-3">
                        </div>
                </div>

                <div class="card md:col-span-2">
                    <h3 class="subsection-title"><span class="icon-emoji">🚗</span>轻松抵达白云山</h3>
                    <p class="text-stone-600 mb-4">推荐从南门进入，靠近索道站和云台花园，交通便利。</p>
                    <div class="space-y-3 text-stone-700">
                        <p><strong class="text-emerald-600">地铁转公交 (推荐)：</strong>地铁3号线北延段至【燕塘站】B口，换乘公交257路或46路至【白云索道站】。</p>
                        <p><strong class="text-emerald-600">直达公交：</strong>多条线路可达【云台花园总站】或【白云索道站】(如24, 63, 285, 46, 175等)。</p>
                        <p><strong class="text-emerald-600">出租车/网约车：</strong>定位“白云山南门”或“云台花园”。</p>
                        <p><strong class="text-emerald-600">自驾：</strong>南门附近有停车场，节假日车位紧张，建议早到。</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="itinerary" class="mb-16 scroll-mt-16">
            <h2 class="section-title">“满分”亲子一日游推荐路线 <span class="icon-emoji">🗺️</span></h2>
            <p class="text-center text-stone-600 mb-8 text-lg">这份精心设计的行程，以5岁宝贝的兴趣和体力为核心，巧妙结合了观景、游乐与科普。我们充分利用了景区内的便捷交通，力求让您和孩子的白云山一日之旅既充实又轻松，留下满满的快乐回忆！</p>
            <div class="space-y-8">
                <div class="card flex flex-col md:flex-row items-start">
                    <div class="md:w-1/4 text-center md:text-left mb-4 md:mb-0">
                        <p class="text-2xl font-bold text-orange-500">上午</p>
                        <p class="text-lg text-emerald-600">9:00 - 9:30</p>
                    </div>
                    <div class="md:w-3/4">
                        <h4 class="text-xl font-semibold text-emerald-700 mb-2">抵达南门，缆车上山 <span class="icon-emoji">🚠</span></h4>
                        <p class="text-stone-700 mb-2">从南门进入，乘坐白云索道（约10分钟）直达山顶公园。高空视角俯瞰城市与山林，对孩子来说新奇有趣！</p>
                        <p class="text-sm text-stone-500"><span class="icon-emoji">💡</span>贴士：提前或线上购票可减少排队。引导孩子观察风景。</p>
                    </div>
                </div>

                <div class="card flex flex-col md:flex-row items-start">
                    <div class="md:w-1/4 text-center md:text-left mb-4 md:mb-0">
                         <p class="text-lg text-emerald-600">9:30 - 12:30</p>
                    </div>
                    <div class="md:w-3/4">
                        <h4 class="text-xl font-semibold text-emerald-700 mb-2">核心游玩区：鸣春谷深度体验 <span class="icon-emoji">🐦🎈</span></h4>
                        <p class="text-stone-700 mb-2">步行前往鸣春谷。这里是亲子游的重点，包含：</p>
                        <ul class="list-disc list-inside space-y-1 text-stone-700 ml-4">
                            <li><strong>天然大鸟笼：</strong>观赏孔雀等多种鸟类。</li>
                            <li><strong>“小鸣”IP无动力乐园：</strong>巨型滑梯、秋千、沙池等，宝贝最爱！(套票约30元一大一小)</li>
                            <li><strong>鸟类生物多样性科普馆：</strong>VR互动、鸟类标本，寓教于乐。(需小程序预约)</li>
                            <li><strong>鹦鹉乐园：</strong>近距离接触和喂食鹦鹉。</li>
                        </ul>
                         <p class="text-sm text-stone-500 mt-2"><span class="icon-emoji">💡</span>贴士：乐园玩耍时成人务必看护。科普馆适合亲子学习。</p>
                    </div>
                </div>

                <div class="card flex flex-col md:flex-row items-start">
                    <div class="md:w-1/4 text-center md:text-left mb-4 md:mb-0">
                        <p class="text-2xl font-bold text-orange-500">午餐</p>
                        <p class="text-lg text-emerald-600">12:30 - 13:30</p>
                    </div>
                    <div class="md:w-3/4">
                        <h4 class="text-xl font-semibold text-emerald-700 mb-2">山顶公园或鸣春谷简餐 <span class="icon-emoji">🍽️</span></h4>
                        <p class="text-stone-700 mb-2">山顶广场附近有小吃店（豆腐花、猪手、烤肠等）。鸣春谷服务区也可能有餐饮。或自备午餐零食。</p>
                        <p class="text-sm text-stone-500"><span class="icon-emoji">💡</span>贴士：山上餐饮选择简单，快捷方便为主。</p>
                    </div>
                </div>

                <div class="card flex flex-col md:flex-row items-start">
                    <div class="md:w-1/4 text-center md:text-left mb-4 md:mb-0">
                        <p class="text-2xl font-bold text-orange-500">下午</p>
                        <p class="text-lg text-emerald-600">13:30 - 15:30</p>
                    </div>
                    <div class="md:w-3/4">
                        <h4 class="text-xl font-semibold text-emerald-700 mb-2">轻松登顶：摩星岭观光 <span class="icon-emoji">�️✨</span></h4>
                        <p class="text-stone-700 mb-2">从山顶公园乘电瓶车D线至摩星岭（票价10元）。摩星岭是白云山最高峰（海拔382米）。</p>
                        <ul class="list-disc list-inside space-y-1 text-stone-700 ml-4">
                            <li><strong>摩星岭景区：</strong>门票5元。</li>
                            <li><strong>步步高观光扶梯：</strong>购票后免费乘坐，轻松登顶仅需5分钟！对孩子老人超友好。</li>
                            <li><strong>山顶观景：</strong>俯瞰广州市区全景，视野开阔。</li>
                        </ul>
                        <p class="text-sm text-stone-500 mt-2"><span class="icon-emoji">💡</span>贴士：扶梯运行时间10:00-19:00。观景台注意孩子安全。</p>
                    </div>
                </div>

                <div class="card flex flex-col md:flex-row items-start">
                     <div class="md:w-1/4 text-center md:text-left mb-4 md:mb-0">
                        <p class="text-lg text-emerald-600">15:30 - 16:30</p>
                    </div>
                    <div class="md:w-3/4">
                        <h4 class="text-xl font-semibold text-emerald-700 mb-2">机动选择或山顶公园休整 <span class="icon-emoji">🌸☕</span></h4>
                        <p class="text-stone-700 mb-2"><strong>选项一 (云台花园，若精力充沛)：</strong>需换乘交通，四季花卉，有儿童乐园。门票10元。<br><strong>选项二 (山顶公园休闲，更稳妥)：</strong>在山顶公园附近休息，买纪念品，准备下山。</p>
                         <p class="text-sm text-stone-500 mt-2"><span class="icon-emoji">💡</span>贴士：5岁孩子连续游玩易疲劳，选项二更推荐。若选云台花园，建议上午去，替换鸣春谷部分时长。</p>
                    </div>
                </div>

                <div class="card flex flex-col md:flex-row items-start">
                    <div class="md:w-1/4 text-center md:text-left mb-4 md:mb-0">
                         <p class="text-2xl font-bold text-orange-500">傍晚</p>
                         <p class="text-lg text-emerald-600">16:30 起</p>
                    </div>
                    <div class="md:w-3/4">
                        <h4 class="text-xl font-semibold text-emerald-700 mb-2">缆车下山，结束行程 <span class="icon-emoji">🌆👋</span></h4>
                        <p class="text-stone-700 mb-2">从山顶公园乘缆车返回南门。傍晚再次欣赏城市景色，完美收官！</p>
                        <p class="text-sm text-stone-500"><span class="icon-emoji">💡</span>贴士：下山后可在南门附近或市区选择晚餐。</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="kid-favorites" class="mb-16 scroll-mt-16">
            <h2 class="section-title">宝贝专属乐趣 <span class="icon-emoji">🧸🎡</span></h2>
            <p class="text-center text-stone-600 mb-8 text-lg">白云山不仅有美丽的自然风光，更有许多专为小朋友设计的趣味体验和科普环节。这些精心设计的亮点，旨在激发宝贝的探索欲和好奇心，让他们在玩乐中学习，在自然中成长，度过一个充满惊喜的一天。</p>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="card">
                    <h3 class="subsection-title text-xl"><span class="icon-emoji">🐦</span>鸣春谷“小鸣”乐园</h3>
                    <p class="text-stone-700">以广州市鸟画眉为原型的“小鸣”IP主题无动力乐园，有巨型滑梯、秋千、沙池等，让孩子尽情撒欢。还能近距离观鸟、喂鹦鹉！</p>
                </div>
                <div class="card">
                    <h3 class="subsection-title text-xl"><span class="icon-emoji">✨</span>摩星岭观光扶梯</h3>
                    <p class="text-stone-700">免费的观光扶梯，几分钟就能轻松登顶白云山最高峰，体验“步步高升”的乐趣，对小朋友体力非常友好。</p>
                </div>
                <div class="card">
                    <h3 class="subsection-title text-xl"><span class="icon-emoji">🚠</span>缆车奇妙体验</h3>
                    <p class="text-stone-700">乘坐缆车本身就是一种新奇体验。在高空与宝贝一同俯瞰城市与山峦，分享途中的发现，是难忘的亲子时刻。</p>
                </div>
                <div class="card">
                    <h3 class="subsection-title text-xl"><span class="icon-emoji">🔬</span>鸟类科普馆</h3>
                    <p class="text-stone-700">鸣春谷内的鸟类生物多样性科普馆，通过VR、互动装置等生动展示鸟类奥秘，是绝佳的自然教育课堂。</p>
                </div>
                 <div class="card">
                    <h3 class="subsection-title text-xl"><span class="icon-emoji">🌸</span>云台花园儿童乐园 (可选)</h3>
                    <p class="text-stone-700">若选择前往云台花园，园内也有儿童游乐设施，可作为赏花之余的调剂。</p>
                </div>
            </div>
        </section>

        <section id="tips" class="mb-16 scroll-mt-16">
            <h2 class="section-title">实用游玩宝典 <span class="icon-emoji">💡📖</span></h2>
             <p class="text-center text-stone-600 mb-8 text-lg">掌握一些实用信息，能让您的白云山亲子之旅更加从容和安心。这里为您准备了景区内交通、餐饮选择、便民设施以及最重要的安全注意事项，助您轻松应对旅途中的各种小细节，全身心享受与宝贝的美好时光。</p>
            <div id="tabs-container" class="w-full">
                <div class="mb-4 border-b border-stone-200">
                    <nav class="flex space-x-1" aria-label="Tabs">
                        <button class="tab-button active" data-tab="transport">景区交通</button>
                        <button class="tab-button" data-tab="food">餐饮选择</button>
                        <button class="tab-button" data-tab="facilities">便民设施</button>
                        <button class="tab-button" data-tab="safety">安全第一</button>
                    </nav>
                </div>
                <div id="transport-content" class="tab-content">
                    <h4 class="text-xl font-semibold text-emerald-700 mb-3">景区内交通：缆车与电瓶车</h4>
                    <p class="text-stone-700 mb-2"><strong>缆车 (索道):</strong> 主要连接南门入口与山顶公园。是亲子游上下山首选，省力且有趣。</p>
                    <p class="text-stone-700 mb-2"><strong>电瓶车 (环保游览车):</strong></p>
                    <ul class="list-disc list-inside ml-4 space-y-1 text-stone-700">
                        <li><strong>D线 (推荐):</strong> 山顶公园 往返 摩星岭 (单程10元)。接驳观光扶梯最便捷。</li>
                        <li><strong>A线 (备选):</strong> 南门 往返 山顶公园 (每段10元)。</li>
                    </ul>
                    <p class="text-stone-600 mt-3"><strong class="text-red-500">注意：</strong>“土豆巴士”(无人驾驶车)有“1.2米以下儿童不建议乘坐”提示，5岁宝贝可能不适合。观光扶梯是登摩星岭更好的选择。</p>
                </div>
                <div id="food-content" class="tab-content" style="display: none;">
                    <h4 class="text-xl font-semibold text-emerald-700 mb-3">餐饮选择：满足全家口味</h4>
                    <p class="text-stone-700 mb-2"><strong>山上主要餐饮点:</strong></p>
                    <ul class="list-disc list-inside ml-4 space-y-1 text-stone-700">
                        <li>山顶广场：有小吃（豆腐花、猪手、烤肠）和便利店。</li>
                        <li>鸣春谷服务区：可能有小吃饮料。</li>
                        <li>山间茶室：可品茗和吃点心。</li>
                    </ul>
                    <p class="text-stone-700 mb-2 mt-2"><strong>山脚及南门附近:</strong></p>
                     <ul class="list-disc list-inside ml-4 space-y-1 text-stone-700">
                        <li>沙河粉村：地道广州小吃。</li>
                        <li>点都德 (白云万达广场店)：家庭友好的粤式茶楼，点心丰富。</li>
                    </ul>
                    <p class="text-stone-600 mt-3"><span class="icon-emoji">💡</span>建议自备孩子喜欢的零食和充足饮用水。</p>
                </div>
                <div id="facilities-content" class="tab-content" style="display: none;">
                    <h4 class="text-xl font-semibold text-emerald-700 mb-3">便民设施：洗手间、母婴室、医疗点</h4>
                     <p class="text-stone-700 mb-2"><strong>洗手间:</strong> 各主要游览区均有，部分采用“潮汐厕所”设计，很人性化。</p>
                     <p class="text-stone-700 mb-2"><strong>母婴室:</strong> 景区内专用母婴室信息<strong class="text-red-500">非常有限且不明确</strong>。建议家长做好准备，携带便携用品，并向工作人员咨询。新建区域（如鸣春谷）理论上应有考虑。</p>
                     <p class="text-stone-700 mb-2"><strong>医疗点/急救站:</strong> 具体固定医疗点信息也<strong class="text-red-500">较为缺乏</strong>。鸣春谷茗园路口附近配有AED。遇紧急情况立即向景区工作人员求助，或拨打景区服务热线 <strong class="text-emerald-600">020-37222222</strong> 及急救电话120。</p>
                     <p class="text-stone-600 mt-3">主要入口和景点工作点会有工作人员提供咨询。</p>
                </div>
                <div id="safety-content" class="tab-content" style="display: none;">
                     <h4 class="text-xl font-semibold text-emerald-700 mb-3">安全第一：亲子游注意事项</h4>
                    <ul class="list-disc list-inside space-y-2 text-stone-700">
                        <li><strong>时刻看护：</strong>确保孩子在视线范围内，尤其在人多、水边、陡坡、游乐设施处。</li>
                        <li><strong>着装安全：</strong>舒适衣物和防滑鞋，避免过长绳带饰品。</li>
                        <li><strong>游乐设施安全：</strong>检查设施，遵守规则，按年龄选择，正确使用（秋千、滑梯等）。</li>
                        <li><strong>徒步安全：</strong>走规划步道，坡大阶多处牵好孩子。</li>
                        <li><strong>防走失：</strong>人多处牵紧手，约定走散后求助工作人员或原地等待。</li>
                        <li><strong>天气应对：</strong>关注天气，雷雨天迅速到室内躲避，远离山顶、孤树、金属物。</li>
                        <li><strong>动植物安全：</strong>勿采食野生植物，防蚊虫叮咬。</li>
                        <li><strong>防火安全：</strong>严禁携带火种上山，景区内禁烟和用明火。</li>
                    </ul>
                    <p class="text-stone-600 mt-3"><strong class="text-emerald-600">紧急联系：</strong>牢记景区电话及110, 120, 119。</p>
                </div>
            </div>
        </section>

        <section id="adjustments" class="mb-16 scroll-mt-16">
            <h2 class="section-title">行程备选与灵活调整 <span class="icon-emoji">🔄</span></h2>
            <p class="text-center text-stone-600 mb-8 text-lg">再完美的计划也可能需要根据宝贝的实时状态和突发情况进行微调。保持一颗灵活的心，是享受亲子游乐趣的秘诀。这里为您提供一些备选方案和调整建议，希望能帮助您从容应对各种小插曲，确保旅途始终充满欢笑。</p>
            <div class="grid md:grid-cols-2 gap-8">
                <div class="card">
                    <h3 class="subsection-title text-xl">若孩子是“花迷”或鸣春谷人多</h3>
                    <p class="text-stone-700">可将上午主玩点改为【云台花园】（四季鲜花，有儿童乐园），再往山顶公园午餐，下午游摩星岭。</p>
                </div>
                <div class="card">
                    <h3 class="subsection-title text-xl">若孩子体力一般或天气炎热</h3>
                    <p class="text-stone-700">缩短各景点停留时间，精简项目（如只玩鸣春谷、乘扶梯登摩星岭），多在阴凉处休息，或提早下山。</p>
                </div>
                <div class="card">
                    <h3 class="subsection-title text-xl">应对人流高峰（周末/节假日）</h3>
                    <p class="text-stone-700">务必早到！自备足量零食饮水。对热门项目排队有心理预期。</p>
                </div>
                <div class="card">
                    <h3 class="subsection-title text-xl">雨天游玩方案</h3>
                    <p class="text-stone-700">侧重有遮蔽的游玩点（鸣春谷科普馆、云台花园温室等）。缆车和扶梯非极端天气通常运行。户外注意脚下湿滑。</p>
                </div>
                 <div class="card md:col-span-2">
                    <h3 class="subsection-title text-xl">关于麓湖儿童乐园的再次说明</h3>
                    <p class="text-stone-700">最可靠信息表明麓湖儿童乐园主体已关闭。麓湖公园本身仍开放，但不宜作为此年龄段儿童游乐主要目的地。</p>
                </div>
            </div>
        </section>

        <footer class="text-center mt-16 py-8 border-t border-emerald-200">
            <p class="text-stone-600">&copy; <span id="currentYear"></span> 白云山亲子游攻略. 祝您和宝贝旅途愉快！</p>
        </footer>

    </div>

    <script>
        // Navigation scroll behavior and active link highlighting
        document.addEventListener('DOMContentLoaded', () => {
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('section');
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const scrollProgress = document.getElementById('scrollProgress');

            // 显示桌面导航
            const desktopNav = document.querySelector('.desktop-nav');
            const mobileNavToggle = document.querySelector('.mobile-nav-toggle');

            function updateNavVisibility() {
                if (window.innerWidth >= 768) {
                    desktopNav.style.display = 'block';
                    mobileNavToggle.style.display = 'none';
                } else {
                    desktopNav.style.display = 'none';
                    mobileNavToggle.style.display = 'block';
                }
            }

            updateNavVisibility();
            window.addEventListener('resize', updateNavVisibility);

            // 滚动进度指示器
            function updateScrollProgress() {
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                scrollProgress.style.width = scrollPercent + '%';
            }

            window.addEventListener('scroll', updateScrollProgress);

            // Mobile menu toggle with animation
            mobileMenuButton.addEventListener('click', () => {
                const isHidden = mobileMenu.style.display === 'none';
                if (isHidden) {
                    mobileMenu.style.display = 'block';
                    mobileMenu.style.opacity = '0';
                    mobileMenu.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        mobileMenu.style.transition = 'all 0.3s ease';
                        mobileMenu.style.opacity = '1';
                        mobileMenu.style.transform = 'translateY(0)';
                    }, 10);
                } else {
                    mobileMenu.style.transition = 'all 0.3s ease';
                    mobileMenu.style.opacity = '0';
                    mobileMenu.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        mobileMenu.style.display = 'none';
                    }, 300);
                }
            });

            // Close mobile menu when a link is clicked
            mobileMenu.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    mobileMenu.style.transition = 'all 0.3s ease';
                    mobileMenu.style.opacity = '0';
                    mobileMenu.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        mobileMenu.style.display = 'none';
                    }, 300);
                });
            });


            function changeActiveLink() {
                let index = sections.length;

                while(--index && window.scrollY + 100 < sections[index].offsetTop) {}

                navLinks.forEach((link) => link.classList.remove('active'));

                // 更新桌面导航活动链接
                const activeDesktopLink = document.querySelector(`.desktop-nav .nav-link[href="#${sections[index].id}"]`);
                if (activeDesktopLink) activeDesktopLink.classList.add('active');

                // 更新移动端导航活动链接
                const activeMobileLink = document.querySelector(`.mobile-nav .nav-link[href="#${sections[index].id}"]`);
                if (activeMobileLink) activeMobileLink.classList.add('active');
            }

            changeActiveLink(); // Set active link on page load
            window.addEventListener('scroll', changeActiveLink);

            navLinks.forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        // Calculate position to scroll to, considering the sticky nav height
                        const navHeight = document.querySelector('nav').offsetHeight;
                        const elementPosition = targetElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - navHeight;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: "smooth"
                        });
                    }
                });
            });

            // Packing list data
            const packingItems = [
                { id: "shoes", text: "舒适轻便的运动鞋/徒步鞋 (全家)" },
                { id: "clothes", text: "透气吸汗衣物 (可分层)" },
                { id: "sunscreen", text: "防晒用品 (帽子、太阳镜、防晒霜)" },
                { id: "mosquito", text: "防蚊虫用品 (驱蚊液/防蚊贴)" },
                { id: "water_snacks", text: "充足饮用水和健康小零食" },
                { id: "first_aid", text: "简易急救包 (创可贴、消毒湿巾)" },
                { id: "cleaning", text: "清洁用品 (湿纸巾、免洗洗手液)" },
                { id: "camera", text: "摄影器材 (手机/相机)" },
                { id: "power_bank", text: "便携充电宝" },
                { id: "backpack", text: "双肩背包" },
                { id: "rain_gear", text: "雨具 (轻便雨衣/折叠伞)" },
                { id: "stroller", text: "轻便折叠推车 (可选，注意路况)" }
            ];

            const packingListContainer = document.getElementById('packingList');
            packingItems.forEach(item => {
                const div = document.createElement('div');
                div.className = 'checklist-item';
                div.style.display = 'flex';
                div.style.alignItems = 'center';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = item.id;
                checkbox.name = item.id;

                const label = document.createElement('label');
                label.htmlFor = item.id;
                label.textContent = item.text;

                div.appendChild(checkbox);
                div.appendChild(label);
                packingListContainer.appendChild(div);
            });

            // Ticket Price Chart
            const ctx = document.getElementById('ticketPriceChart').getContext('2d');
            const ticketPriceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['景区门票', '摩星岭', '鸣春谷', '索道上山', '索道下山', '鸣春谷乐园(1大1小)'],
                    datasets: [{
                        label: '成人票价 (元)',
                        data: [5, 5, 10, 25, 20, 30],
                        backgroundColor: 'rgba(22, 163, 74, 0.7)', // emerald-600
                        borderColor: 'rgba(22, 163, 74, 1)',
                        borderWidth: 1
                    }, {
                        label: '5岁儿童票价 (元)',
                        data: [0, 0, 0, 0, 0, 0], // Assuming 0 for the child in the bundle
                        backgroundColor: 'rgba(249, 115, 22, 0.7)', // orange-500
                        borderColor: 'rgba(249, 115, 22, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '价格 (元)'
                            }
                        },
                        x: {
                            ticks: {
                                callback: function(value, index, values) {
                                    // Label wrapping logic (16-char is a bit long for this chart type, maybe shorter)
                                    const label = this.getLabelForValue(value);
                                    if (label.length > 6) { // Adjust max length as needed
                                        return label.match(new RegExp('.{1,' + 6 + '}', 'g')); // Wrap every 6 chars
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y + '元';
                                    }
                                    if (context.datasetIndex === 1 && context.parsed.y === 0) {
                                        const adultPrice = ticketPriceChart.data.datasets[0].data[context.dataIndex];
                                        if (adultPrice > 0) { // only add free if there is an adult price
                                            const itemName = ticketPriceChart.data.labels[context.dataIndex];
                                            if (itemName.includes('鸣春谷乐园')) {
                                                 label += ' (套票已含儿童)';
                                            } else {
                                                 label += ' (通常免费)';
                                            }
                                        }
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Tabs functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.dataset.tab;

                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');

                    tabContents.forEach(content => {
                        if (content.id === `${tabId}-content`) {
                            content.style.display = 'block';
                            content.style.opacity = '0';
                            content.style.transform = 'translateY(10px)';
                            setTimeout(() => {
                                content.style.transition = 'all 0.3s ease';
                                content.style.opacity = '1';
                                content.style.transform = 'translateY(0)';
                            }, 10);
                        } else {
                            content.style.display = 'none';
                        }
                    });
                });
            });

            // Set current year in footer
            document.getElementById('currentYear').textContent = new Date().getFullYear();

            // 添加页面加载完成后的动画
            setTimeout(() => {
                document.body.style.opacity = '1';
                document.body.style.transform = 'translateY(0)';
            }, 100);

            // 添加平滑滚动到顶部功能
            let scrollToTopBtn = document.createElement('button');
            scrollToTopBtn.innerHTML = '⬆️';
            scrollToTopBtn.style.cssText = `
                position: fixed;
                bottom: 2rem;
                right: 2rem;
                width: 3rem;
                height: 3rem;
                border-radius: 50%;
                background: linear-gradient(135deg, #059669, #10b981);
                color: white;
                border: none;
                font-size: 1.2rem;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
                opacity: 0;
                transform: translateY(20px);
                transition: all 0.3s ease;
                z-index: 1000;
            `;

            scrollToTopBtn.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            document.body.appendChild(scrollToTopBtn);

            // 显示/隐藏回到顶部按钮
            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    scrollToTopBtn.style.opacity = '1';
                    scrollToTopBtn.style.transform = 'translateY(0)';
                } else {
                    scrollToTopBtn.style.opacity = '0';
                    scrollToTopBtn.style.transform = 'translateY(20px)';
                }
            });

            // 添加键盘导航支持
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && mobileMenu.style.display === 'block') {
                    mobileMenu.style.display = 'none';
                }
            });

            // 优化图片懒加载
            const images = document.querySelectorAll('img');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.style.opacity = '0';
                        img.style.transition = 'opacity 0.3s ease';
                        setTimeout(() => {
                            img.style.opacity = '1';
                        }, 100);
                        observer.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        });
    </script>
</body>
</html>