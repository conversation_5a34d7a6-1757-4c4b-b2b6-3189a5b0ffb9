<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2025广州龙舟赛亲子一日游攻略 🐉</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 导入中文字体 */
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;800&family=Ma+Shan+Zheng&display=swap');

        :root {
            --dragon-red: #dc2626;
            --dragon-gold: #f59e0b;
            --dragon-green: #059669;
            --dragon-blue: #2563eb;
            --water-blue: #0ea5e9;
            --festival-orange: #ea580c;
            --warm-bg: #fef7ed;
            --paper-white: #fffbf0;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, var(--warm-bg) 0%, #fef3c7 50%, var(--warm-bg) 100%);
            background-attachment: fixed;
            position: relative;
        }

        /* 龙舟主题背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop&crop=center&auto=format&q=80'),
                radial-gradient(circle at 20% 80%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.1) 0%, transparent 50%);
            background-size: 100px 100px, 800px 800px, 600px 600px;
            background-repeat: repeat, no-repeat, no-repeat;
            opacity: 0.3;
            z-index: -1;
            pointer-events: none;
        }
        /* 导航链接样式 */
        .nav-link {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover {
            color: var(--dragon-blue);
            background: rgba(245, 158, 11, 0.1);
            transform: translateY(-1px);
        }

        .nav-link.active {
            color: var(--dragon-red);
            background: linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(245, 158, 11, 0.1));
            box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
        }

        /* 标题样式 */
        .section-title {
            font-size: 2.25rem;
            font-weight: 800;
            color: var(--dragon-red);
            margin-bottom: 1.5rem;
            text-align: center;
            position: relative;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .section-title::after {
            content: '🐉';
            position: absolute;
            right: -3rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(-50%) rotate(0deg); }
            50% { transform: translateY(-60%) rotate(5deg); }
        }

        /* 卡片样式 */
        .card {
            background: linear-gradient(135deg, var(--paper-white) 0%, #ffffff 100%);
            padding: 2rem;
            border-radius: 1rem;
            box-shadow:
                0 10px 25px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(245, 158, 11, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--dragon-red), var(--dragon-gold), var(--dragon-green), var(--dragon-blue));
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(245, 158, 11, 0.2);
        }

        /* 标签按钮样式 */
        .tab-button {
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            font-size: 0.875rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            position: relative;
            border: 2px solid transparent;
        }

        .tab-button.active {
            background: linear-gradient(135deg, var(--dragon-blue), var(--water-blue));
            color: white;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }

        .tab-button:not(.active) {
            color: #6b7280;
            background: #f9fafb;
            border-color: #e5e7eb;
        }

        .tab-button:not(.active):hover {
            background: #f3f4f6;
            color: var(--dragon-blue);
            border-color: var(--dragon-gold);
        }
        /* 手风琴样式 */
        .accordion-button {
            width: 100%;
            text-align: left;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, #f9fafb, #f3f4f6);
            border-radius: 0.5rem;
            font-weight: 600;
            color: #374151;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
        }

        .accordion-button:hover {
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            border-color: var(--dragon-gold);
            color: var(--dragon-blue);
        }

        .accordion-content {
            padding: 1.5rem;
            background: white;
            border: 2px solid #e5e7eb;
            border-top: none;
            border-radius: 0 0 0.5rem 0.5rem;
        }

        /* 表格样式优化 */
        .responsive-table {
            overflow-x: auto;
            border-radius: 0.75rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .responsive-table table {
            min-width: 100%;
            background: white;
            border-radius: 0.75rem;
            overflow: hidden;
        }

        .responsive-table th {
            background: linear-gradient(135deg, var(--dragon-blue), var(--water-blue));
            color: white;
            padding: 1rem;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
        }

        .responsive-table td {
            padding: 1rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .responsive-table tr:hover {
            background: rgba(245, 158, 11, 0.05);
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .section-title {
                font-size: 1.875rem;
            }

            .section-title::after {
                right: -2rem;
                font-size: 1.25rem;
            }

            .card {
                padding: 1.5rem;
                margin: 0.5rem 0;
            }

            .responsive-table {
                font-size: 0.875rem;
            }

            .responsive-table th,
            .responsive-table td {
                padding: 0.75rem 0.5rem;
            }

            .tab-button {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--dragon-gold), var(--festival-orange));
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--festival-orange), var(--dragon-red));
        }

        /* 动画效果 */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .animate-slide-in {
            animation: slideInUp 0.6s ease-out;
        }

        .animate-pulse-gentle {
            animation: pulse 2s ease-in-out infinite;
        }

        /* 特殊装饰元素 */
        .dragon-decoration {
            position: relative;
        }

        .dragon-decoration::before {
            content: '🐲';
            position: absolute;
            left: -2rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            opacity: 0.6;
        }

        /* 节日主题按钮 */
        .festival-button {
            background: linear-gradient(135deg, var(--dragon-red), var(--festival-orange));
            color: white;
            padding: 1rem 2rem;
            border-radius: 2rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
            position: relative;
            overflow: hidden;
        }

        .festival-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .festival-button:hover::before {
            left: 100%;
        }

        .festival-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
        }

        /* 文字阴影 */
        .text-shadow-lg {
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
        }

        /* 添加section动画类 */
        .section-animate {
            animation: slideInUp 0.8s ease-out;
        }

        /* 移动端section标题优化 */
        @media (max-width: 640px) {
            .section-title::after {
                display: none;
            }

            .dragon-decoration::before {
                display: none;
            }
        }

        /* 增强移动端体验 */
        @media (max-width: 480px) {
            .festival-button {
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
            }

            .hero-emoji {
                font-size: 3rem !important;
            }

            .hero-title {
                font-size: 2rem !important;
            }
        }
    </style>
</head>
<body class="text-gray-700 antialiased">

    <header class="bg-gradient-to-r from-white/90 via-orange-50/90 to-white/90 backdrop-blur-md shadow-lg sticky top-0 z-50 border-b-2 border-gradient-to-r from-transparent via-orange-200 to-transparent">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex-shrink-0 flex items-center space-x-2">
                    <div class="text-2xl animate-pulse-gentle">🐉</div>
                    <h1 class="text-xl font-bold bg-gradient-to-r from-red-600 via-orange-500 to-red-600 bg-clip-text text-transparent">
                        广州龙舟亲子游
                    </h1>
                    <div class="text-lg">🏮</div>
                </div>
                <nav class="hidden md:flex space-x-1">
                    <a href="#home" class="nav-link active">
                        <i class="fas fa-home mr-1"></i>首页
                    </a>
                    <a href="#recommendation" class="nav-link">
                        <i class="fas fa-star mr-1"></i>首选推荐
                    </a>
                    <a href="#itinerary" class="nav-link">
                        <i class="fas fa-route mr-1"></i>一日游行程
                    </a>
                    <a href="#guide" class="nav-link">
                        <i class="fas fa-compass mr-1"></i>实用指南
                    </a>
                    <a href="#alternatives" class="nav-link">
                        <i class="fas fa-map-marked-alt mr-1"></i>其他选择
                    </a>
                </nav>
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-gray-700 hover:text-red-600 focus:outline-none p-2 rounded-lg hover:bg-orange-100 transition-all duration-300">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <div id="mobile-menu" class="md:hidden hidden bg-gradient-to-b from-white to-orange-50 shadow-xl border-t border-orange-200">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#home" class="mobile-nav-link block px-4 py-3 text-sm text-gray-700 hover:bg-orange-100 hover:text-red-600 rounded-lg transition-all duration-300">
                    <i class="fas fa-home mr-2"></i>首页
                </a>
                <a href="#recommendation" class="mobile-nav-link block px-4 py-3 text-sm text-gray-700 hover:bg-orange-100 hover:text-red-600 rounded-lg transition-all duration-300">
                    <i class="fas fa-star mr-2"></i>首选推荐
                </a>
                <a href="#itinerary" class="mobile-nav-link block px-4 py-3 text-sm text-gray-700 hover:bg-orange-100 hover:text-red-600 rounded-lg transition-all duration-300">
                    <i class="fas fa-route mr-2"></i>一日游行程
                </a>
                <a href="#guide" class="mobile-nav-link block px-4 py-3 text-sm text-gray-700 hover:bg-orange-100 hover:text-red-600 rounded-lg transition-all duration-300">
                    <i class="fas fa-compass mr-2"></i>实用指南
                </a>
                <a href="#alternatives" class="mobile-nav-link block px-4 py-3 text-sm text-gray-700 hover:bg-orange-100 hover:text-red-600 rounded-lg transition-all duration-300">
                    <i class="fas fa-map-marked-alt mr-2"></i>其他选择
                </a>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <section id="home" class="pt-16 -mt-16 min-h-screen flex flex-col justify-center section-animate">
            <!-- 主要英雄区域 -->
            <div class="relative text-center py-16 rounded-2xl shadow-2xl overflow-hidden mb-8">
                <!-- 背景图片 -->
                <div class="absolute inset-0 z-0">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=800&fit=crop&crop=center&auto=format&q=80"
                         alt="龙舟比赛"
                         class="w-full h-full object-cover opacity-20">
                    <div class="absolute inset-0 bg-gradient-to-br from-red-600/80 via-orange-500/70 to-blue-600/80"></div>
                </div>

                <!-- 装饰性龙舟元素 -->
                <div class="absolute top-4 left-4 text-4xl animate-pulse-gentle opacity-60">🐲</div>
                <div class="absolute top-4 right-4 text-4xl animate-pulse-gentle opacity-60" style="animation-delay: 1s;">🏮</div>
                <div class="absolute bottom-4 left-8 text-3xl animate-pulse-gentle opacity-60" style="animation-delay: 2s;">🥟</div>
                <div class="absolute bottom-4 right-8 text-3xl animate-pulse-gentle opacity-60" style="animation-delay: 0.5s;">⛵</div>

                <!-- 内容 -->
                <div class="relative z-10 px-8 md:px-16">
                    <div class="mb-6">
                        <span class="text-6xl md:text-8xl animate-pulse-gentle hero-emoji">🐉</span>
                    </div>
                    <h2 class="text-4xl md:text-6xl font-extrabold text-white mb-6 leading-tight text-shadow-lg hero-title" style="font-family: 'Ma Shan Zheng', cursive;">
                        2025广州龙舟全攻略
                    </h2>
                    <div class="flex justify-center items-center space-x-4 mb-8">
                        <span class="text-2xl">🏮</span>
                        <p class="text-xl md:text-2xl text-orange-100 max-w-3xl">
                            携5岁萌娃，乐享端午亲子一日游！探索广州深厚的龙舟文化，体验原汁原味的岭南风情。
                        </p>
                        <span class="text-2xl">🏮</span>
                    </div>
                    <a href="#recommendation" class="festival-button text-lg inline-flex items-center space-x-2">
                        <span>查看首选推荐</span>
                        <span class="text-xl">🎉</span>
                    </a>
                </div>
            </div>
            <!-- 介绍文字区域 -->
            <div class="card max-w-5xl mx-auto">
                <div class="flex items-start space-x-4">
                    <div class="text-4xl flex-shrink-0 mt-2">🏛️</div>
                    <div>
                        <h3 class="text-2xl font-bold text-red-600 mb-4 dragon-decoration">广州端午·传统与现代的完美融合</h3>
                        <p class="text-lg text-gray-700 leading-relaxed">
                            广州的端午节，是一场流淌在珠江水脉间的盛大庆典。空气中弥漫着粽叶的清香，耳畔回响着激昂的龙舟鼓点。这座城市以其深厚的岭南文化底蕴，将古老的传统演绎得活色生香。2025年的端午节，部分龙舟活动与“六一”儿童节相邻，为亲子家庭提供了体验传统文化与童趣融合的绝佳机会。本攻略将带您深入了解最适合家庭的龙舟活动，并规划充实的一日游行程。
                        </p>
                        <div class="mt-6 flex flex-wrap gap-4 justify-center">
                            <div class="flex items-center space-x-2 bg-red-50 px-4 py-2 rounded-full">
                                <span class="text-xl">🐲</span>
                                <span class="text-sm font-medium text-red-700">传统龙舟</span>
                            </div>
                            <div class="flex items-center space-x-2 bg-orange-50 px-4 py-2 rounded-full">
                                <span class="text-xl">👨‍👩‍👧‍👦</span>
                                <span class="text-sm font-medium text-orange-700">亲子体验</span>
                            </div>
                            <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-full">
                                <span class="text-xl">🏮</span>
                                <span class="text-sm font-medium text-blue-700">岭南文化</span>
                            </div>
                            <div class="flex items-center space-x-2 bg-green-50 px-4 py-2 rounded-full">
                                <span class="text-xl">🥟</span>
                                <span class="text-sm font-medium text-green-700">美食体验</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="recommendation" class="pt-20 -mt-16">
            <h2 class="section-title">🐉 首选推荐：海珠湿地龙舟嘉年华 👨‍👩‍👧‍👦</h2>
            <p class="text-center text-lg text-gray-600 mb-10 max-w-3xl mx-auto">
                对于以5岁孩子为核心的家庭，2025年海珠湿地龙舟嘉年华是绝佳选择。其核心魅力在于6月1日全天举办的亲子龙舟赛，专为家庭设计，让孩子近距离感受龙舟乐趣。相比大型赛事，这里氛围更亲切，场地更易游玩。
            </p>

            <div class="grid md:grid-cols-2 gap-8 items-start">
                <div class="card space-y-4">
                    <h3 class="text-2xl font-semibold text-blue-600">活动概览：精彩看点</h3>
                    <ul class="list-disc list-inside text-gray-700 space-y-2">
                        <li><strong>嘉年华时间：</strong> 2025年5月31日至6月2日</li>
                        <li><strong>核心活动 - 亲子龙舟赛：</strong> 6月1日全天 (海珠湿地二期爱莲亭)</li>
                        <li><strong>传统龙舟竞赛：</strong> 6月2日 (上午传统龙舟，下午标准龙舟)</li>
                        <li><strong>千人龙船宴：</strong> 5月28日至6月2日 (每日11:30-13:00 / 17:30-19:00，海珠湿地二期华泰路)</li>
                        <li><strong>其他精彩：</strong> 湿地文创市集、醒狮点睛、龙舟说唱、英歌舞、汉服巡游等。</li>
                    </ul>
                    <p class="text-sm text-gray-500 italic">温馨提示：亲子龙舟赛参与方式及费用需通过官方渠道查询，即使不参赛，观看也充满乐趣！</p>
                </div>

                <div class="card">
                    <h3 class="text-2xl font-semibold text-blue-600 mb-4">海珠湿地亲子龙舟日核心信息</h3>
                    <div class="responsive-table">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">内容</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr><td class="px-6 py-4 whitespace-nowrap font-medium">🎯 活动名称</td><td class="px-6 py-4">2025第七届海珠湿地龙船景系列活动</td></tr>
                                <tr><td class="px-6 py-4 whitespace-nowrap font-medium">📅 核心推荐日期</td><td class="px-6 py-4">2025年6月1日（星期日）</td></tr>
                                <tr><td class="px-6 py-4 whitespace-nowrap font-medium">⏰ 亲子龙舟赛时间</td><td class="px-6 py-4">6月1日 全天</td></tr>
                                <tr><td class="px-6 py-4 whitespace-nowrap font-medium">📍 亲子龙舟赛地点</td><td class="px-6 py-4">海珠湿地公园二期 爱莲亭</td></tr>
                                <tr><td class="px-6 py-4 whitespace-nowrap font-medium">🍽️ 龙船宴时间（可选）</td><td class="px-6 py-4">5月31日-6月2日 (11:30-13:00 / 17:30-19:00)</td></tr>
                                <tr><td class="px-6 py-4 whitespace-nowrap font-medium">🏪 龙船宴地点（可选）</td><td class="px-6 py-4">海珠湿地公园二期 华泰路</td></tr>
                                <tr><td class="px-6 py-4 whitespace-nowrap font-medium">✨ 家庭亮点</td><td class="px-6 py-4">观看亲子龙舟赛、体验传统文化表演、逛文创市集、品尝龙船宴（需留意预订）</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <section id="itinerary" class="pt-20 -mt-16">
            <h2 class="section-title">📅 2025年6月1日：专属龙舟奇缘一日游行程 🗺️</h2>
            <p class="text-center text-lg text-gray-600 mb-10 max-w-3xl mx-auto">
                这份详细的行程规划将帮助您充分利用一天的时间，在海珠湿地公园与孩子共享龙舟节的快乐。从抵达公园到参与各项活动，再到悠然返程，每一步都为您精心设计。
            </p>
            <div class="space-y-8">
                <div class="card">
                    <h3 class="text-xl font-semibold text-blue-700 mb-3">☀️ 上午 (8:30 - 12:00)：抵达公园，感受赛前氛围</h3>
                    <div class="space-y-3 text-gray-700">
                        <p><strong>8:30 - 9:00：前往海珠湿地公园（二期南门）</strong><br><span class="text-sm text-gray-500">建议公共交通。地铁至沥滘站换乘或乘公交至“海珠湿地公园南门站”。</span></p>
                        <p><strong>9:00 - 10:30：进入海珠湿地二期，悠游探索</strong><br><span class="text-sm text-gray-500">径直前往爱莲亭区域，感受湿地自然魅力，观察节日布置。</span></p>
                        <p><strong>10:30 - 12:00：寻觅观赛佳位，融入爱莲亭周边热烈氛围</strong><br><span class="text-sm text-gray-500">观看上午比赛或热身，寻找舒适观赛点。</span></p>
                    </div>
                </div>
                <div class="card">
                    <h3 class="text-xl font-semibold text-blue-700 mb-3">🍜 中午 (12:00 - 14:30)：龙舟竞渡高潮与节日午餐</h3>
                    <div class="space-y-3 text-gray-700">
                        <p><strong>12:00 - 13:30：亲子龙舟赛黄金观赏时段</strong><br><span class="text-sm text-gray-500">比赛进入高潮，与孩子一起为选手加油！</span></p>
                        <p><strong>13:30 - 14:30：午餐时光</strong><br><span class="text-sm text-gray-500">选择一：千人龙船宴（需预订）；选择二：市集寻味；选择三：自带野餐（灵活便捷）。</span></p>
                    </div>
                </div>
                <div class="card">
                    <h3 class="text-xl font-semibold text-blue-700 mb-3">🎨 下午 (14:30 - 17:00)：文化畅游，童趣互动，悠然返程</h3>
                    <div class="space-y-3 text-gray-700">
                        <p><strong>14:30 - 16:00：深度体验龙舟嘉年华文化魅力</strong><br><span class="text-sm text-gray-500">逛文创市集，赏非遗手作，看传统表演（英歌舞、醒狮等），参与可能的亲子互动。</span></p>
                        <p><strong>16:00 - 17:00：休憩玩乐或最后留影，准备离园</strong><br><span class="text-sm text-gray-500">根据孩子精力调整，注意公园17:00停止入园及清场。</span></p>
                    </div>
                </div>
            </div>

            <div class="mt-10 card">
                <h3 class="text-xl font-semibold text-blue-600 mb-4">行程规划参考总览</h3>
                 <div class="responsive-table">
                    <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">⏰ 时间段</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">🎯 活动与地点</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">💡 家庭温馨提示</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <tr><td class="px-6 py-4">🌅 8:30-9:00</td><td class="px-6 py-4">前往海珠湿地公园（二期南门）</td><td class="px-6 py-4">建议公共交通，预留充足交通时间。</td></tr>
                            <tr><td class="px-6 py-4">🚶 9:00-10:30</td><td class="px-6 py-4">进入海珠湿地二期，向爱莲亭方向探索</td><td class="px-6 py-4">感受公园清晨宁静，留意节日布置。</td></tr>
                            <tr><td class="px-6 py-4">👀 10:30-12:00</td><td class="px-6 py-4">在爱莲亭附近寻找观赛点，感受赛前氛围</td><td class="px-6 py-4">寻找有遮阳的观赛位置。</td></tr>
                            <tr><td class="px-6 py-4">🐉 12:00-13:30</td><td class="px-6 py-4">观看亲子龙舟赛</td><td class="px-6 py-4">与孩子一起为选手加油。</td></tr>
                            <tr><td class="px-6 py-4">🍽️ 13:30-14:30</td><td class="px-6 py-4">午餐（龙船宴/市集美食/自带野餐）</td><td class="px-6 py-4">龙船宴需预订；自带野餐更灵活。</td></tr>
                            <tr><td class="px-6 py-4">🎨 14:30-16:00</td><td class="px-6 py-4">逛文创市集，看民俗表演，参与互动</td><td class="px-6 py-4">体验传统文化，留意儿童DIY。</td></tr>
                            <tr><td class="px-6 py-4">😊 16:00-17:00</td><td class="px-6 py-4">休憩、自由活动，准备离园</td><td class="px-6 py-4">注意公园清场时间，愉快结束。</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <section id="guide" class="pt-20 -mt-16">
            <h2 class="section-title">🎒 端午出行实用指南 💡</h2>
            <p class="text-center text-lg text-gray-600 mb-10 max-w-3xl mx-auto">
                为了确保您的亲子龙舟之旅顺利愉快，这里准备了一些实用的出行信息和建议，包括交通方式、必备物品、安全注意事项以及如何让5岁孩子更好地融入活动。
            </p>
            <div>
                <div class="mb-4 border-b border-gray-200">
                    <nav class="-mb-px flex space-x-4" aria-label="Tabs">
                        <button class="tab-button active" data-tab="transport">🚗 交通方式</button>
                        <button class="tab-button" data-tab="packing">🧳 必备清单</button>
                        <button class="tab-button" data-tab="safety">🛡️ 安全舒适</button>
                        <button class="tab-button" data-tab="engage">🧒 激发兴趣</button>
                    </nav>
                </div>
                <div id="tab-content-container" class="card">
                    <div id="tab-transport" class="tab-content space-y-3">
                        <h4 class="text-lg font-semibold text-gray-800">前往海珠湿地公园（二期南门）</h4>
                        <p><strong>公共交通：</strong></p>
                        <ul class="list-disc list-inside ml-4">
                            <li><strong>地铁：</strong> 3号线至沥滘站，换乘公交/出租车/共享单车（约1.7km）到南门。</li>
                            <li><strong>公交：</strong> 乘坐直达“海珠湿地公园南门站”的线路。</li>
                        </ul>
                        <p><strong>出租车/网约车：</strong> 定位至“广州海珠国家湿地公园-南门”。</p>
                        <p><strong>停车：</strong> 南门有停车场但车位有限，节日人多，强烈建议公交出行。</p>
                    </div>
                    <div id="tab-packing" class="tab-content hidden space-y-3">
                         <h4 class="text-lg font-semibold text-gray-800">家庭出行必备物品清单</h4>
                         <div class="responsive-table">
                            <table class="min-w-full bg-white border border-gray-100 rounded-md">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">📦 类别</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">📝 物品建议</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-100">
                                    <tr><td class="px-4 py-2 font-medium">☀️ 防晒/防雨</td><td class="px-4 py-2">儿童太阳帽、防晒霜、轻便雨伞/儿童雨衣 (广州6月天气多变)</td></tr>
                                    <tr><td class="px-4 py-2 font-medium">🧸 孩子舒适</td><td class="px-4 py-2">小玩具/安抚物、湿纸巾、免洗洗手液</td></tr>
                                    <tr><td class="px-4 py-2 font-medium">🥤 饮食补给</td><td class="px-4 py-2">可重复使用水壶、孩子爱吃的小零食 (公园建议自备饮用水)</td></tr>
                                    <tr><td class="px-4 py-2 font-medium">🏥 安全健康</td><td class="px-4 py-2">基础儿童急救包、儿童防蚊液 (湿地蚊虫较多)</td></tr>
                                    <tr><td class="px-4 py-2 font-medium">🔭 观赛乐趣</td><td class="px-4 py-2">(可选) 儿童用小型望远镜</td></tr>
                                    <tr><td class="px-4 py-2 font-medium">📱 其他</td><td class="px-4 py-2">少量现金、充电宝、相机/手机</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div id="tab-safety" class="tab-content hidden space-y-3">
                        <h4 class="text-lg font-semibold text-gray-800">确保舒适与安全：带5岁孩子出游注意事项</h4>
                        <ul class="list-disc list-inside ml-4 space-y-2">
                            <li><strong>应对人流：</strong> 尽量提早到达。人多处牵紧孩子的手。</li>
                            <li><strong>水边安全：</strong> 时刻看护，勿近无护栏水边，强调水边安全。</li>
                            <li><strong>洗手间与休息：</strong> 入园时留意导览图或咨询。注意间歇休息。</li>
                            <li><strong>天气适应：</strong> 广州6月炎热潮湿，可能阵雨。穿着轻薄透气，备好雨具。</li>
                            <li><strong>走失预案：</strong> 约定集合点，手机存孩子近照。</li>
                        </ul>
                    </div>
                    <div id="tab-engage" class="tab-content hidden space-y-3">
                        <h4 class="text-lg font-semibold text-gray-800">激发5岁孩子的兴趣：让龙舟“活”起来</h4>
                        <ul class="list-disc list-inside ml-4 space-y-2">
                            <li><strong>趣味科普：</strong> 简单介绍龙舟和比赛意义。</li>
                            <li><strong>寻找鼓手：</strong> 引导观察鼓手的作用。</li>
                            <li><strong>数数船员：</strong> 一起数每条船有多少人划桨。</li>
                            <li><strong>大声喝彩：</strong> 鼓励孩子一起为队伍加油。</li>
                            <li><strong>关联亲子赛：</strong> 指给孩子看其他小朋友参与的场景。</li>
                            <li><strong>回顾分享：</strong> 活动后一起画龙舟或聊聊开心的事。</li>
                        </ul>
                        <p class="text-sm text-gray-600">让孩子在玩乐中学习，通过具象化方式理解传统文化，能极大提升参与感。</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="alternatives" class="pt-20 -mt-16">
            <h2 class="section-title">🤔 其他龙舟观赏选择 (备选方案) 🏞️</h2>
             <p class="text-center text-lg text-gray-600 mb-10 max-w-3xl mx-auto">
                如果海珠湿地的安排不巧未能成行，或者您希望了解更多广州的龙舟活动，以下两个也各具特色。但请注意，综合日期匹配、亲子互动性和观赏体验，海珠湿地仍是6月1日首选。
            </p>
            <div class="grid md:grid-cols-2 gap-8">
                <div class="card">
                    <h3 class="text-xl font-semibold text-blue-600 mb-3">广州国际龙舟邀请赛 (6月1日)</h3>
                    <p class="text-gray-600 mb-2"><strong class="text-gray-700">地点：</strong>中大北门广场至广州大桥之间的珠江河段</p>
                    <p class="text-gray-600 mb-2"><strong class="text-gray-700">看点：</strong>规模最大，队伍最多（含国际队），场面壮观，有传统龙、彩龙、游龙表演。</p>
                    <p class="text-gray-600"><strong class="text-gray-700">家庭考量：</strong>人流量巨大，可能拥挤嘈杂。观赛点集中在珠江沿岸，注意防晒和天气。有交通管制。</p>
                    <p class="text-sm text-blue-500 mt-2">推荐观赛点：中大北门广场（起点）、广州大桥附近（终点）。</p>
                </div>
                <div class="card">
                    <h3 class="text-xl font-semibold text-blue-600 mb-3">猎德龙舟招景 (主要活动日期可能在5月31日)</h3>
                    <p class="text-gray-600 mb-2"><strong class="text-gray-700">地点：</strong>天河区猎德村、猎德涌，珠江公园兴盛门段为核心观赏区之一。</p>
                    <p class="text-gray-600 mb-2"><strong class="text-gray-700">看点：</strong>“招景”是特色民俗，展现浓郁广府水乡风情和“龙舟探亲”习俗，场面热闹。</p>
                    <p class="text-gray-600"><strong class="text-gray-700">家庭考量：</strong>主要活动日期可能为5月31日（需核实）。更侧重文化巡游，非正式竞技。</p>
                     <p class="text-sm text-orange-500 mt-2">温馨提示：日期请临近时再次核实，此活动更偏向民俗体验。</p>
                </div>
            </div>
            <div class="mt-8 card">
                 <h3 class="text-xl font-semibold text-blue-600 mb-3">可选补充：广州市儿童公园龙舟元素</h3>
                 <p class="text-gray-600 mb-2"><strong class="text-gray-700">地点：</strong>白云区齐心路61号</p>
                 <p class="text-gray-600 mb-2"><strong class="text-gray-700">特色：</strong>设有龙舟戏水乐园，带滑梯龙舟、击鼓装置、水枪等互动戏水设施。</p>
                 <p class="text-gray-600"><strong class="text-gray-700">可行性：</strong>对于以海珠湿地为核心的一日游路途较远。适合多日停留或行程有较大弹性的家庭。</p>
            </div>
        </section>
    </main>

    <footer class="relative bg-gradient-to-br from-gray-800 via-gray-900 to-red-900 text-white py-16 mt-12 overflow-hidden">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 opacity-10">
            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=400&fit=crop&crop=center&auto=format&q=80"
                 alt="龙舟背景"
                 class="w-full h-full object-cover">
        </div>

        <!-- 装饰性元素 -->
        <div class="absolute top-4 left-4 text-3xl opacity-30 animate-pulse-gentle">🐲</div>
        <div class="absolute top-4 right-4 text-3xl opacity-30 animate-pulse-gentle" style="animation-delay: 1s;">🏮</div>
        <div class="absolute bottom-4 left-8 text-2xl opacity-30 animate-pulse-gentle" style="animation-delay: 2s;">🥟</div>
        <div class="absolute bottom-4 right-8 text-2xl opacity-30 animate-pulse-gentle" style="animation-delay: 0.5s;">⛵</div>

        <div class="relative container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="mb-8">
                <div class="flex justify-center items-center space-x-4 mb-4">
                    <span class="text-4xl animate-pulse-gentle">🐉</span>
                    <h3 class="text-3xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent">
                        珍藏您的广州龙舟记忆！
                    </h3>
                    <span class="text-4xl animate-pulse-gentle">🐉</span>
                </div>
                <div class="flex justify-center space-x-6 mb-6">
                    <span class="text-2xl">🎉</span>
                    <span class="text-2xl">🏮</span>
                    <span class="text-2xl">🥟</span>
                    <span class="text-2xl">⛵</span>
                    <span class="text-2xl">🎊</span>
                </div>
            </div>
            <div class="max-w-3xl mx-auto mb-8">
                <p class="text-lg text-gray-200 leading-relaxed mb-6">
                    2025年的广州端午节，将传统、喜庆与童趣融为一体。希望这份攻略能助您和家人，尤其是可爱的5岁小“龙舟迷”，度过一个充满活力、温馨而难忘的龙舟日。
                </p>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                        <div class="text-2xl mb-2">🐲</div>
                        <div class="text-sm">传统文化</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                        <div class="text-2xl mb-2">👨‍👩‍👧‍👦</div>
                        <div class="text-sm">亲子体验</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                        <div class="text-2xl mb-2">🏮</div>
                        <div class="text-sm">岭南风情</div>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                        <div class="text-2xl mb-2">🥟</div>
                        <div class="text-sm">美食文化</div>
                    </div>
                </div>
            </div>

            <div class="border-t border-white/20 pt-6">
                <p class="text-gray-400 text-sm flex items-center justify-center space-x-2">
                    <span>&copy; 2025 广州龙舟亲子游攻略助手</span>
                    <span class="text-orange-400">🐉</span>
                    <span>祝您旅途愉快！</span>
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Navigation scroll
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth'
                    });
                    // Update active link state for main nav
                    document.querySelectorAll('header nav a.nav-link').forEach(link => {
                        link.classList.remove('active');
                    });
                    if (!this.closest('#mobile-menu')) { // Only apply to main nav links
                         this.classList.add('active');
                    }

                    // For mobile menu, just close it
                    if (document.getElementById('mobile-menu').contains(this)){
                        document.getElementById('mobile-menu').classList.add('hidden');
                        // also update active state on main nav if possible for consistency, though it's hidden
                        const mainNavLink = document.querySelector(`header nav a.nav-link[href="${targetId}"]`);
                        if(mainNavLink) mainNavLink.classList.add('active');
                    }
                }
            });
        });

        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
        }

        // Active link highlighting on scroll
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('header nav a.nav-link');

        window.addEventListener('scroll', () => {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= (sectionTop - sectionHeight / 3 - 70)) { // Adjusted offset for sticky header
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').substring(1) === current) {
                    link.classList.add('active');
                }
            });
            // Ensure home is active if at top or no other section is current
            if (current === '' || current === 'home') {
                 const homeLink = document.querySelector('header nav a.nav-link[href="#home"]');
                 if (homeLink && !homeLink.classList.contains('active')) {
                     navLinks.forEach(l => l.classList.remove('active'));
                     homeLink.classList.add('active');
                 }
            }
        });


        // Tab functionality for Practical Guide
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;

                tabButtons.forEach(btn => {
                    btn.classList.remove('active', 'bg-blue-500', 'text-white');
                    btn.classList.add('text-gray-600', 'hover:bg-gray-200');
                });
                button.classList.add('active', 'bg-blue-500', 'text-white');
                button.classList.remove('text-gray-600', 'hover:bg-gray-200');


                tabContents.forEach(content => {
                    if (content.id === `tab-${tabId}`) {
                        content.classList.remove('hidden');
                    } else {
                        content.classList.add('hidden');
                    }
                });
            });
        });

    </script>
</body>
</html>
