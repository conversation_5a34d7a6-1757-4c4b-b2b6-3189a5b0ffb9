<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>21天内启动你的第一个SaaS - 完整指南</title>
    <!-- TailwindCSS 通过CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 字体 Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        // 配置Tailwind主题
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                },
            },
        }
    </script>
    <style type="text/css">
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        /* 自定义样式 */
        .step-card {
            transition: all 0.3s ease;
        }
        
        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 加载动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease forwards;
        }
        
        /* 确保深色模式下的文本可读性 */
        .dark .dark:text-white {
            color: #f3f4f6;
        }
    </style>
</head>
<body class="font-sans bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 检测系统主题并设置初始主题 -->
    <script>
        // 检查用户偏好
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    </script>

    <!-- 导航栏 -->
    <nav class="sticky top-0 z-50 bg-white dark:bg-gray-800 shadow-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="#" class="flex-shrink-0 flex items-center">
                        <i class="fas fa-rocket text-primary-600 dark:text-primary-400 text-2xl mr-2"></i>
                        <span class="font-bold text-xl">21天SaaS启动指南</span>
                    </a>
                </div>
                <div class="flex items-center">
                    <!-- 主题切换按钮 -->
                    <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none">
                        <i id="theme-icon" class="fas fa-moon text-gray-600 dark:text-gray-300"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <header class="bg-gradient-to-r from-primary-600 to-blue-700 dark:from-primary-800 dark:to-blue-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in">
                如何在21天内启动你的第一个SaaS
            </h1>
            <p class="text-xl text-white opacity-90 max-w-3xl mx-auto animate-fade-in" style="animation-delay: 0.2s">
                从构思到部署的完整蓝图 - 利用AI工具快速构建可盈利的SaaS产品
            </p>
            <div class="mt-10 animate-fade-in" style="animation-delay: 0.4s">
                <a href="#blueprint" class="bg-white text-primary-600 hover:bg-gray-100 font-semibold px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition duration-300 inline-flex items-center">
                    <i class="fas fa-arrow-down mr-2"></i>
                    开始学习
                </a>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <!-- 介绍 -->
        <section id="intro" class="mb-20 animate-fade-in">
            <div class="text-center max-w-3xl mx-auto">
                <h2 class="text-3xl font-bold mb-6 text-gray-800 dark:text-white">为什么是21天?</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 mb-8">
                    在当今AI工具蓬勃发展的时代，构建SaaS产品的速度和效率已经达到前所未有的水平。
                    本指南将向你展示如何利用最新的AI工具，在短短21天内从零开始构建并启动一个可盈利的SaaS产品。
                </p>
                <div class="flex justify-center space-x-4">
                    <div class="bg-primary-50 dark:bg-gray-800 p-4 rounded-lg shadow text-center w-1/3">
                        <i class="fas fa-lightbulb text-yellow-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold">构思</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">第1-3天</p>
                    </div>
                    <div class="bg-primary-50 dark:bg-gray-800 p-4 rounded-lg shadow text-center w-1/3">
                        <i class="fas fa-code text-primary-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold">构建</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">第4-18天</p>
                    </div>
                    <div class="bg-primary-50 dark:bg-gray-800 p-4 rounded-lg shadow text-center w-1/3">
                        <i class="fas fa-rocket text-green-500 text-2xl mb-2"></i>
                        <h3 class="font-semibold">发布</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">第19-21天</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 蓝图部分 -->
        <section id="blueprint" class="mb-20">
            <h2 class="text-3xl font-bold mb-10 text-center text-gray-800 dark:text-white">完整蓝图</h2>
            
            <!-- 步骤 1 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="1">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">1</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">原则#1：构建可盈利的产品</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            如果你没有想法，这里介绍如何使用AI爬取网络并找到市场空白。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">推荐AI工具：</h4>
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>Perplexity深度研究</li>
                                <li>ChatGPT深度搜索</li>
                                <li>Grok深层搜索</li>
                            </ul>
                        </div>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">有效提示词：</h4>
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-2">
                                <li>"找出XYZ行业中新SaaS可以填补的10个最大空白。"</li>
                                <li>"XYZ行业在哪些方面花费最多？给我一个详细的细分。"</li>
                                <li>"XYZ行业的主要痛点是什么？给我一个详细列表。"</li>
                                <li>"我有这个想法......对这个想法提供批判性分析。值得构建吗？前10名竞争对手是谁？总可寻址市场(TAM)有多大？对我的想法进行10分制评估并分享你的理性推理。"</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 2 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="2">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">2</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">规划你的应用</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            找到产品创意后，现在开始规划你的应用。不是很多人知道ChatGPT语音功能可以访问网络，这使它成为语音研究助手。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">使用ChatGPT语音作为头脑风暴伙伴：</h4>
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>"这个MVP的核心功能是什么？"</li>
                                <li>"使用MoSCoW方法提炼我的MVP的所有功能"</li>
                            </ul>
                        </div>
                        
                        <div class="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4 border-l-4 border-blue-500">
                            <p class="text-blue-700 dark:text-blue-300 font-medium">
                                <i class="fas fa-lightbulb mr-2"></i>关键提示：你的工作是专注于MVP中<span class="font-bold">不应该</span>添加什么。你的v1版本应该只解决一个问题，但比市场上任何人都做得更好。保持简单。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 3 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="3">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">3</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">选择你的AI工具</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            我们生活在一个每个AI工具都有一项超能力的时代。以下是推荐的工具：
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-paint-brush text-pink-500 mr-2"></i>
                                    <h4 class="font-semibold text-gray-700 dark:text-gray-200">Lovable</h4>
                                </div>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">用于设计落地页</p>
                            </div>
                            
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-desktop text-purple-500 mr-2"></i>
                                    <h4 class="font-semibold text-gray-700 dark:text-gray-200">v0</h4>
                                </div>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">用于设计UI界面</p>
                            </div>
                            
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-code text-green-500 mr-2"></i>
                                    <h4 class="font-semibold text-gray-700 dark:text-gray-200">Cursor/Windsurf</h4>
                                </div>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">作为AI代码编辑器</p>
                            </div>
                            
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-cloud-upload-alt text-blue-500 mr-2"></i>
                                    <h4 class="font-semibold text-gray-700 dark:text-gray-200">Vercel</h4>
                                </div>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">用于部署（简单易用）</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 4 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="4">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">4</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">生成编码文档</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            这是构建生产就绪应用程序并减少安全漏洞的必要步骤。编码文档为AI编码模型形成"知识库"。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">推荐工具：</h4>
                            <p class="text-gray-600 dark:text-gray-300">
                                使用 <span class="font-semibold">CodeGuide.dev</span> 生成详细的编码文档。
                            </p>
                        </div>
                        
                        <div class="bg-green-50 dark:bg-green-900/30 rounded-lg p-4 border-l-4 border-green-500">
                            <h4 class="font-semibold mb-2 text-green-700 dark:text-green-300">好处：</h4>
                            <ul class="list-disc list-inside text-green-600 dark:text-green-300 space-y-1">
                                <li>修复AI幻觉问题</li>
                                <li>减少安全漏洞</li>
                                <li>减少错误</li>
                                <li>提高代码质量和布局</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 5 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="5">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">5</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">设计你的前端</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            你需要可视化你的应用将如何呈现。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">推荐方法：</h4>
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>使用v0单独设计应用的所有界面</li>
                                <li>也可以使用Lovable，但v0可以提供单个文件中的代码，稍后可以粘贴到Cursor中组装前端</li>
                            </ul>
                        </div>
                        
                        <div class="flex justify-center my-4">
                            <img src="https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="UI设计示例" class="rounded-lg shadow-md max-w-full h-auto" loading="lazy">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 6 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="6">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">6</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">在AI代码编辑器中组装代码库</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            现在是时候将所有内容组合在一起了。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">推荐工具：</h4>
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>使用Cursor/Windsurf作为你的AI IDE</li>
                                <li>使用启动套件（样板）来节省安装所有必需包的时间 - CodeGuide有6个套件</li>
                                <li>连接GitHub，这样你就不会丢失代码</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 7 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="7">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">7</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">构建你的后端</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            对于后端，Supabase是理想选择，因为AI可以编写SQL查询来构建数据库表。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">推荐技术栈：</h4>
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>使用Supabase作为数据库和存储</li>
                                <li>使用Clerkdev进行用户认证（Supabase需要自定义域名进行认证，这会花费$10/月）</li>
                                <li>附加你的编码文档，在AI编码模型周围建立上下文边界</li>
                            </ul>
                        </div>
                        
                        <div class="bg-indigo-50 dark:bg-indigo-900/30 rounded-lg p-4 border-l-4 border-indigo-500 mt-4">
                            <p class="text-indigo-700 dark:text-indigo-300">
                                <i class="fas fa-info-circle mr-2"></i>CodeGuide提供"实施计划"，AI模型可以遵循该计划并端到端地编码你的应用。只需带上你的前端代码，Cursor Agent/Cascade将使用编码文档将你的前端与后端集成。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 8 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="8">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">8</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">测试你的应用</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            一旦所有页面都已编码完成，是时候进行测试了。确保没有安全漏洞。
                        </p>
                        
                        <div class="bg-yellow-50 dark:bg-yellow-900/30 rounded-lg p-4 border-l-4 border-yellow-500">
                            <p class="text-yellow-700 dark:text-yellow-300">
                                <i class="fas fa-exclamation-triangle mr-2"></i>你可以将安全检查清单粘贴到Cursor中，并要求它扫描代码库并撰写详细的安全报告。CodeGuide默认将这些内容添加到文档中。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 9 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="9">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">9</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">部署与营销</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            部署前确保以下几点：
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">部署前检查清单：</h4>
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>所有页面都正常工作（按钮、功能、关系）</li>
                                <li>在桌面、移动和平板上打开你的应用，检查响应式设计</li>
                                <li>应用与落地页正确链接（注册、登录）</li>
                                <li>没有安全漏洞</li>
                            </ul>
                        </div>
                        
                        <div class="bg-red-50 dark:bg-red-900/30 rounded-lg p-4 border-l-4 border-red-500 mt-4">
                            <h4 class="font-semibold mb-2 text-red-700 dark:text-red-300">成为无耻的营销者</h4>
                            <p class="text-red-600 dark:text-red-300">
                                现在你需要做的就是每天谈论你的应用，不休息，不偷懒。9/10的初创公司因为分销而失败。发布大量内容。将你的营销努力游戏化。设定每周目标。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 总结部分 -->
        <section id="summary" class="mb-20">
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-8 shadow-md">
                <h2 class="text-2xl font-bold mb-6 text-center text-gray-800 dark:text-white">关键要点</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white dark:bg-gray-800 p-5 rounded-lg shadow-sm">
                        <div class="flex items-center mb-3">
                            <i class="fas fa-bullseye text-red-500 text-xl mr-3"></i>
                            <h3 class="font-semibold text-lg">专注于一个问题</h3>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">
                            你的MVP应该只解决一个问题，但要比市场上任何人都做得更好。保持简单。
                        </p>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 p-5 rounded-lg shadow-sm">
                        <div class="flex items-center mb-3">
                            <i class="fas fa-tools text-blue-500 text-xl mr-3"></i>
                            <h3 class="font-semibold text-lg">利用AI工具</h3>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">
                            充分利用现代AI工具加速开发过程，从构思到部署的每个阶段。
                        </p>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 p-5 rounded-lg shadow-sm">
                        <div class="flex items-center mb-3">
                            <i class="fas fa-bullhorn text-green-500 text-xl mr-3"></i>
                            <h3 class="font-semibold text-lg">持续营销</h3>
                        </div>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">
                            构建只是开始。成功需要持续的营销努力和用户反馈的迭代改进。
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-gray-100 dark:bg-gray-800 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-6 md:mb-0">
                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">21天SaaS启动指南</h2>
                    <p class="text-gray-600 dark:text-gray-300">
                        利用AI工具快速构建可盈利的SaaS产品
                    </p>
                </div>
                
                <div class="flex flex-col">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-white">作者信息</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-2">作者姓名: 蔡文浩</p>
                    <div class="flex space-x-4">
                        <a href="https://github.com/caiwenhao" target="_blank" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="https://twitter.com/caiwenhao" target="_blank" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="https://linkedin.com/in/caiwenhao" target="_blank" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-500 dark:text-gray-400">
                    &copy; 2025 蔡文浩. 保留所有权利.
                </p>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script>
        // 主题切换功能
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = document.getElementById('theme-icon');
        
        themeToggle.addEventListener('click', function() {
            // 切换深色模式
            document.documentElement.classList.toggle('dark');
            
            // 更新图标
            if (document.documentElement.classList.contains('dark')) {
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                localStorage.theme = 'dark';
            } else {
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
                localStorage.theme = 'light';
            }
        });
        
        // 初始化图标状态
        if (document.documentElement.classList.contains('dark')) {
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        }
        
        // 滚动动画
        function checkScroll() {
            const stepCards = document.querySelectorAll('.step-card');
            
            stepCards.forEach(card => {
                const cardTop = card.getBoundingClientRect().top;
                const windowHeight = window.innerHeight;
                
                if (cardTop < windowHeight * 0.85) {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                }
            });
        }
        
        // 初始检查
        window.addEventListener('load', checkScroll);
        // 滚动时检查
        window.addEventListener('scroll', checkScroll);
    </script>
</body>
</html>
