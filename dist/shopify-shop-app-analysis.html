<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopify Shop App 竞品调研报告 - 专业分析</title>
    <meta name="description" content="深度分析Shopify Shop App的产品策略、功能特性和商业模式，为H5购物门户产品提供战略指导">

    <!-- TailwindCSS 通过CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // 配置Tailwind主题 - 端午赛龙舟主题色彩
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        dragon: {
                            50: '#fef7ee',
                            100: '#fdedd3',
                            200: '#fbd7a5',
                            300: '#f8bc6d',
                            400: '#f59e0b',
                            500: '#d97706',
                            600: '#b45309',
                            700: '#92400e',
                            800: '#78350f',
                            900: '#451a03',
                        },
                        festival: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'dragon-float': 'dragonFloat 3s ease-in-out infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        dragonFloat: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        }
                    },
                }
            }
        }
    </script>

    <style type="text/css">
        /* 自定义样式 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.1);
        }

        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }

        /* 渐变背景 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .dragon-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f59e0b' fill-opacity='0.05'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        /* 侧边栏样式 */
        .sidebar {
            transition: transform 0.3s ease;
            height: calc(100vh - 4rem);
            width: 16rem;
            flex-shrink: 0;
            position: fixed;
            top: 4rem;
            left: 0;
            z-index: 30;
            overflow-y: auto;
        }

        .sidebar.hidden {
            transform: translateX(-100%);
        }

        /* 桌面端侧边栏固定跟随 */
        @media (min-width: 768px) {
            .sidebar {
                position: fixed;
                top: 4rem;
                left: 0;
            }

            .sidebar.hidden {
                transform: translateX(0);
            }
        }

        /* 防止水平滚动 */
        * {
            box-sizing: border-box;
        }

        html, body {
            overflow-x: hidden;
            max-width: 100vw;
            margin: 0;
            padding: 0;
        }

        /* 修复布局问题 */
        .main-container {
            min-height: 100vh;
            width: 100vw;
            overflow-x: hidden;
            position: relative;
        }

        .main-content {
            width: 100%;
            min-height: 100vh;
            overflow-x: hidden;
            padding: 0;
            margin: 0;
        }

        /* 桌面端：为侧边栏留出空间 */
        @media (min-width: 768px) {
            .main-content {
                margin-left: 16rem;
                width: calc(100vw - 16rem);
            }
        }

        /* 进度条 */
        .progress-bar {
            height: 4px;
            background: linear-gradient(90deg, #f59e0b, #22c55e);
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            transition: width 0.3s ease;
        }

        /* 章节标题样式 */
        .section-title {
            position: relative;
            padding-left: 1rem;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 2rem;
            background: linear-gradient(180deg, #f59e0b, #22c55e);
            border-radius: 2px;
        }

        /* 数据卡片样式 */
        .data-card {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(34, 197, 94, 0.1));
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        /* 响应式表格 */
        .responsive-table {
            overflow-x: auto;
            max-width: 100%;
        }

        .responsive-table table {
            min-width: 600px;
            width: 100%;
        }

        /* 确保所有容器不超出视口 */
        .container {
            max-width: 100%;
            overflow-x: hidden;
            margin-left: auto;
            margin-right: auto;
            padding-left: 1rem;
            padding-right: 1rem;
        }

        /* 确保所有section不超出视口 */
        section {
            width: 100%;
            overflow-x: hidden;
        }

        /* 图表容器 */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .section-title {
                font-size: 1.5rem;
            }

            .card-hover {
                transform: none;
            }

            .card-hover:hover {
                transform: none;
                box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
            }

            .main-content {
                margin-left: 0 !important;
                width: 100% !important;
                max-width: 100vw !important;
            }

            .sidebar {
                position: fixed !important;
                top: 4rem !important;
                left: 0 !important;
                z-index: 40 !important;
                height: calc(100vh - 4rem) !important;
            }

            .sidebar.hidden {
                transform: translateX(-100%) !important;
            }

            /* 移动端遮罩 */
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.5);
                z-index: 25;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.3s ease, visibility 0.3s ease;
            }

            .sidebar-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            .chart-container {
                height: 250px;
            }

            /* 移动端容器优化 */
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
                max-width: 100%;
            }

            /* 防止卡片超出屏幕 */
            .card-hover {
                max-width: 100%;
                overflow: hidden;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300 dragon-pattern">
    <!-- 进度条 -->
    <div class="progress-bar" id="progressBar" style="width: 0%"></div>

    <!-- 顶部导航栏 -->
    <header class="sticky top-0 z-40 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm shadow-sm border-b border-dragon-200 dark:border-gray-700">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <div class="relative">
                    <i class="fas fa-dragon text-dragon-500 text-2xl animate-dragon-float"></i>
                    <div class="absolute -top-1 -right-1 w-3 h-3 bg-festival-500 rounded-full animate-pulse"></div>
                </div>
                <div>
                    <h1 class="text-xl font-bold bg-gradient-to-r from-dragon-600 to-festival-600 bg-clip-text text-transparent">
                        竞品调研报告
                    </h1>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Shopify Shop App 深度分析</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <!-- 侧边栏切换按钮 -->
                <button id="sidebar-toggle" class="md:hidden p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-bars text-gray-600 dark:text-gray-300"></i>
                </button>
                <!-- 深色/浅色模式切换按钮 -->
                <button id="theme-toggle" class="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-moon hidden dark:block text-yellow-300"></i>
                    <i class="fas fa-sun block dark:hidden text-yellow-500"></i>
                </button>
                <!-- 返回首页 -->
                <a href="index.html" class="hidden sm:flex items-center space-x-2 px-4 py-2 bg-dragon-500 hover:bg-dragon-600 text-white rounded-lg transition-colors">
                    <i class="fas fa-home"></i>
                    <span>返回首页</span>
                </a>
            </div>
        </div>
    </header>

    <!-- 移动端侧边栏遮罩 -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>

    <!-- 侧边栏导航 -->
    <nav id="sidebar" class="sidebar bg-white dark:bg-gray-800 shadow-lg">
        <div class="p-6">
            <h3 class="text-lg font-semibold mb-4 text-dragon-600 dark:text-dragon-400">目录导航</h3>
            <ul class="space-y-2">
                <li><a href="#executive-summary" class="nav-link block py-2 px-3 rounded-lg hover:bg-dragon-50 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-chart-line mr-2 text-dragon-500"></i>报告概述
                </a></li>
                <li><a href="#competitor-overview" class="nav-link block py-2 px-3 rounded-lg hover:bg-dragon-50 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-building mr-2 text-dragon-500"></i>竞品概览
                </a></li>
                <li><a href="#product-teardown" class="nav-link block py-2 px-3 rounded-lg hover:bg-dragon-50 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-cogs mr-2 text-dragon-500"></i>产品拆解
                </a></li>
                <li><a href="#business-analysis" class="nav-link block py-2 px-3 rounded-lg hover:bg-dragon-50 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-dollar-sign mr-2 text-dragon-500"></i>商业分析
                </a></li>
                <li><a href="#swot-analysis" class="nav-link block py-2 px-3 rounded-lg hover:bg-dragon-50 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-balance-scale mr-2 text-dragon-500"></i>SWOT分析
                </a></li>
                <li><a href="#strategic-recommendations" class="nav-link block py-2 px-3 rounded-lg hover:bg-dragon-50 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-lightbulb mr-2 text-dragon-500"></i>战略建议
                </a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <main class="main-content">
            <!-- 英雄区域 -->
            <section class="relative gradient-bg text-white py-16 px-4">
                <div class="absolute inset-0 bg-black/20"></div>
                <div class="relative container mx-auto text-center">
                    <div class="animate-fade-in">
                        <h1 class="text-4xl md:text-6xl font-bold mb-6">
                            竞品调研报告
                        </h1>
                        <p class="text-xl md:text-2xl mb-8 opacity-90">
                            Shopify Shop App 深度分析与我方H5门户产品战略规划
                        </p>
                        <div class="flex flex-wrap justify-center gap-4">
                            <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                                <div class="text-2xl font-bold">1亿+</div>
                                <div class="text-sm opacity-80">活跃用户</div>
                            </div>
                            <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                                <div class="text-2xl font-bold">数百万</div>
                                <div class="text-sm opacity-80">商家店铺</div>
                            </div>
                            <div class="bg-white/20 backdrop-blur-sm rounded-lg px-6 py-3">
                                <div class="text-2xl font-bold">18%</div>
                                <div class="text-sm opacity-80">转化率提升</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 报告概述 -->
            <section id="executive-summary" class="py-16 px-4">
                <div class="container mx-auto">
                    <h2 class="section-title text-3xl md:text-4xl font-bold mb-8 animate-slide-up">第一部分：报告概述</h2>

                    <!-- 核心发现卡片 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                        <div class="card-hover bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                            <div class="flex items-center mb-6">
                                <div class="bg-dragon-100 dark:bg-dragon-900 p-3 rounded-full mr-4">
                                    <i class="fas fa-target text-dragon-600 dark:text-dragon-400 text-xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold">调研目标</h3>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                                深度剖析行业标杆 Shop App，识别其成功要素与潜在弱点，为我方产品的战略定位、功能规划和运营策略提供决策依据。
                            </p>
                        </div>

                        <div class="card-hover bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                            <div class="flex items-center mb-6">
                                <div class="bg-festival-100 dark:bg-festival-900 p-3 rounded-full mr-4">
                                    <i class="fas fa-lightbulb text-festival-600 dark:text-festival-400 text-xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold">核心洞察</h3>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                                Shop App 正从订单追踪工具向重要的用户参与和商品发现平台转型，其核心竞争壁垒在于庞大的商家池和用户网络效应。
                            </p>
                        </div>
                    </div>

                    <!-- 核心建议 -->
                    <div class="data-card rounded-xl p-8 mb-12">
                        <h3 class="text-2xl font-semibold mb-6 flex items-center">
                            <i class="fas fa-star text-dragon-500 mr-3"></i>
                            核心建议
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="flex items-start space-x-3">
                                <div class="bg-dragon-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mt-1">1</div>
                                <div>
                                    <h4 class="font-semibold mb-2">明确产品定位</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">聚焦"小而美"，选择特定垂直领域或区域市场</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="bg-festival-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mt-1">2</div>
                                <div>
                                    <h4 class="font-semibold mb-2">优化核心功能</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">打磨符合H5特性的核心功能，利用PWA提升体验</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="bg-dragon-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mt-1">3</div>
                                <div>
                                    <h4 class="font-semibold mb-2">内容驱动发现</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">通过高质量内容和人工策展打造差异化体验</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="bg-festival-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mt-1">4</div>
                                <div>
                                    <h4 class="font-semibold mb-2">PWA技术选型</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">充分利用PWA提供接近原生体验的特性</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 竞品概览 -->
            <section id="competitor-overview" class="py-16 px-4 bg-gray-100 dark:bg-gray-800">
                <div class="container mx-auto">
                    <h2 class="section-title text-3xl md:text-4xl font-bold mb-8">第二部分：竞品概览</h2>

                    <!-- Shop App 介绍 -->
                    <div class="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg mb-8">
                        <div class="flex flex-col lg:flex-row items-start lg:items-center gap-8">
                            <div class="flex-1">
                                <h3 class="text-2xl font-semibold mb-4 flex items-center">
                                    <i class="fas fa-mobile-alt text-primary-500 mr-3"></i>
                                    Shopify Shop App 产品定位
                                </h3>
                                <p class="text-gray-600 dark:text-gray-400 leading-relaxed mb-6">
                                    Shop App 正经历从一款订单追踪工具到 Shopify 生态系统中重要的用户参与和商品发现平台的战略转型。
                                    其目标是拓展应用于购买前阶段，将其打造为一个全面的购物助手和发现渠道。
                                </p>
                                <div class="flex flex-wrap gap-3">
                                    <span class="bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 px-3 py-1 rounded-full text-sm">
                                        订单追踪
                                    </span>
                                    <span class="bg-dragon-100 dark:bg-dragon-900 text-dragon-700 dark:text-dragon-300 px-3 py-1 rounded-full text-sm">
                                        商品发现
                                    </span>
                                    <span class="bg-festival-100 dark:bg-festival-900 text-festival-700 dark:text-festival-300 px-3 py-1 rounded-full text-sm">
                                        一键支付
                                    </span>
                                </div>
                            </div>
                            <div class="lg:w-80">
                                <div class="bg-gradient-to-br from-primary-500 to-dragon-500 rounded-lg p-6 text-white">
                                    <h4 class="text-lg font-semibold mb-4">市场表现</h4>
                                    <div class="space-y-3">
                                        <div class="flex justify-between">
                                            <span>活跃用户</span>
                                            <span class="font-bold">1亿+</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>支持平台</span>
                                            <span class="font-bold">iOS/Android/Web</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>转化率提升</span>
                                            <span class="font-bold">高达18%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 目标用户画像 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div class="card-hover bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg">
                            <h3 class="text-xl font-semibold mb-6 flex items-center">
                                <i class="fas fa-users text-primary-500 mr-3"></i>
                                C端消费者画像
                            </h3>
                            <ul class="space-y-4">
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-festival-500 mt-1"></i>
                                    <span class="text-gray-600 dark:text-gray-400">追求一站式订单追踪便利性</span>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-festival-500 mt-1"></i>
                                    <span class="text-gray-600 dark:text-gray-400">渴望高效便捷的支付体验</span>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-festival-500 mt-1"></i>
                                    <span class="text-gray-600 dark:text-gray-400">喜欢发现新的独立品牌</span>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-festival-500 mt-1"></i>
                                    <span class="text-gray-600 dark:text-gray-400">注重购物后的确定性</span>
                                </li>
                            </ul>
                        </div>

                        <div class="card-hover bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg">
                            <h3 class="text-xl font-semibold mb-6 flex items-center">
                                <i class="fas fa-store text-dragon-500 mr-3"></i>
                                B端商户画像
                            </h3>
                            <ul class="space-y-4">
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-festival-500 mt-1"></i>
                                    <span class="text-gray-600 dark:text-gray-400">使用Shopify建站的独立站卖家</span>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-festival-500 mt-1"></i>
                                    <span class="text-gray-600 dark:text-gray-400">渴望免费的流量渠道</span>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-festival-500 mt-1"></i>
                                    <span class="text-gray-600 dark:text-gray-400">希望提升复购率和用户忠诚度</span>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-festival-500 mt-1"></i>
                                    <span class="text-gray-600 dark:text-gray-400">借助Shopify官方平台提升信任感</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 产品深度拆解 -->
            <section id="product-teardown" class="py-16 px-4">
                <div class="container mx-auto">
                    <h2 class="section-title text-3xl md:text-4xl font-bold mb-8">第三部分：产品深度拆解</h2>

                    <!-- 核心价值主张 -->
                    <div class="mb-12">
                        <h3 class="text-2xl font-semibold mb-8 text-center">核心价值主张分析</h3>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- 对消费者的价值 -->
                            <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                                <h4 class="text-xl font-semibold mb-6 flex items-center">
                                    <i class="fas fa-heart text-red-500 mr-3"></i>
                                    对消费者的价值
                                </h4>
                                <div class="space-y-6">
                                    <div class="border-l-4 border-primary-500 pl-4">
                                        <h5 class="font-semibold text-primary-600 dark:text-primary-400">便利性</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">一站式追踪全网包裹，平均每个订单发送约4次推送通知</p>
                                    </div>
                                    <div class="border-l-4 border-dragon-500 pl-4">
                                        <h5 class="font-semibold text-dragon-600 dark:text-dragon-400">发现性</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">个性化推荐，基于购买历史和浏览模式发现新品牌</p>
                                    </div>
                                    <div class="border-l-4 border-festival-500 pl-4">
                                        <h5 class="font-semibold text-festival-600 dark:text-festival-400">高效性</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Shop Pay 一键支付，显著减少购物车放弃率</p>
                                    </div>
                                    <div class="border-l-4 border-purple-500 pl-4">
                                        <h5 class="font-semibold text-purple-600 dark:text-purple-400">奖励机制</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Shop Cash 1%返现，增强用户粘性</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 对商家的价值 -->
                            <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                                <h4 class="text-xl font-semibold mb-6 flex items-center">
                                    <i class="fas fa-handshake text-green-500 mr-3"></i>
                                    对商家的价值
                                </h4>
                                <div class="space-y-6">
                                    <div class="border-l-4 border-blue-500 pl-4">
                                        <h5 class="font-semibold text-blue-600 dark:text-blue-400">新渠道</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">免费的移动端销售渠道，无额外上架费或交易费</p>
                                    </div>
                                    <div class="border-l-4 border-green-500 pl-4">
                                        <h5 class="font-semibold text-green-600 dark:text-green-400">高复购</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">转化率提升高达18%，AOV提升高达50%</p>
                                    </div>
                                    <div class="border-l-4 border-yellow-500 pl-4">
                                        <h5 class="font-semibold text-yellow-600 dark:text-yellow-400">品牌背书</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">借助Shopify官方平台提升信任感</p>
                                    </div>
                                    <div class="border-l-4 border-indigo-500 pl-4">
                                        <h5 class="font-semibold text-indigo-600 dark:text-indigo-400">自动化</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">自动化订单状态更新，减轻客服压力</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 核心功能分析 -->
                    <div class="mb-12">
                        <h3 class="text-2xl font-semibold mb-8 text-center">核心功能与体验分析</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- 订单追踪 -->
                            <div class="card-hover bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                                <div class="text-center mb-4">
                                    <div class="bg-primary-100 dark:bg-primary-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <i class="fas fa-truck text-primary-600 dark:text-primary-400 text-2xl"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold">订单追踪</h4>
                                </div>
                                <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>自动同步Shopify订单</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>邮箱扫描识别外部订单</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>实时物流地图可视化</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>关键状态推送通知</li>
                                </ul>
                                <div class="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                    <p class="text-xs text-yellow-700 dark:text-yellow-300">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        H5局限：无法直接读取邮箱权限
                                    </p>
                                </div>
                            </div>

                            <!-- 商品发现 -->
                            <div class="card-hover bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                                <div class="text-center mb-4">
                                    <div class="bg-dragon-100 dark:bg-dragon-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <i class="fas fa-search text-dragon-600 dark:text-dragon-400 text-2xl"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold">商品发现</h4>
                                </div>
                                <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>个性化推荐信息流</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>AI驱动的购物助手</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>以商家为中心的设计</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>多维度数据推荐</li>
                                </ul>
                                <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                    <p class="text-xs text-blue-700 dark:text-blue-300">
                                        <i class="fas fa-lightbulb mr-1"></i>
                                        机会：内容策展差异化
                                    </p>
                                </div>
                            </div>

                            <!-- 一键支付 -->
                            <div class="card-hover bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                                <div class="text-center mb-4">
                                    <div class="bg-festival-100 dark:bg-festival-900 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <i class="fas fa-credit-card text-festival-600 dark:text-festival-400 text-2xl"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold">一键支付</h4>
                                </div>
                                <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>跨店铺一键结账</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>PCI-DSS一级认证</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>分期付款支持</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>网络效应强大</li>
                                </ul>
                                <div class="mt-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                                    <p class="text-xs text-red-700 dark:text-red-300">
                                        <i class="fas fa-times mr-1"></i>
                                        挑战：难以复制网络效应
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 用户旅程地图 -->
                    <div class="bg-gradient-to-r from-primary-50 to-dragon-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-8">
                        <h3 class="text-2xl font-semibold mb-8 text-center">关键用户旅程地图</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
                                    <div class="bg-primary-500 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <span class="font-bold">1</span>
                                    </div>
                                    <h4 class="font-semibold mb-3">首次激活</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        商家结账页推荐 → 账户创建 → 邮箱关联 → 权限授权
                                    </p>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
                                    <div class="bg-dragon-500 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <span class="font-bold">2</span>
                                    </div>
                                    <h4 class="font-semibold mb-3">购物发现</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        发现商品 → 浏览详情 → Shop Pay结账 → 订单确认
                                    </p>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
                                    <div class="bg-festival-500 text-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <span class="font-bold">3</span>
                                    </div>
                                    <h4 class="font-semibold mb-3">订单追踪</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        订单自动识别 → 查看物流地图 → 接收推送通知
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 商业模式与战略分析 -->
            <section id="business-analysis" class="py-16 px-4 bg-gray-100 dark:bg-gray-800">
                <div class="container mx-auto">
                    <h2 class="section-title text-3xl md:text-4xl font-bold mb-8">第四部分：商业模式与战略分析</h2>

                    <!-- 商业模式分析 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                        <div class="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg">
                            <h3 class="text-xl font-semibold mb-6 flex items-center">
                                <i class="fas fa-chart-pie text-primary-500 mr-3"></i>
                                当前商业模式
                            </h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                    <span class="font-medium">商家使用</span>
                                    <span class="text-green-600 dark:text-green-400 font-bold">免费</span>
                                </div>
                                <div class="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                    <span class="font-medium">消费者使用</span>
                                    <span class="text-green-600 dark:text-green-400 font-bold">免费</span>
                                </div>
                                <div class="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                    <span class="font-medium">Shop Pay 标准</span>
                                    <span class="text-blue-600 dark:text-blue-400 font-bold">包含在Shopify Payments内</span>
                                </div>
                                <div class="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                    <span class="font-medium">分期付款</span>
                                    <span class="text-yellow-600 dark:text-yellow-400 font-bold">5.9% + $0.30/笔</span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg">
                            <h3 class="text-xl font-semibold mb-6 flex items-center">
                                <i class="fas fa-lightbulb text-dragon-500 mr-3"></i>
                                间接价值创造
                            </h3>
                            <div class="space-y-4">
                                <div class="border-l-4 border-primary-500 pl-4">
                                    <h4 class="font-semibold text-primary-600 dark:text-primary-400">提升商家留存</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">增强对Shopify平台的依赖度</p>
                                </div>
                                <div class="border-l-4 border-dragon-500 pl-4">
                                    <h4 class="font-semibold text-dragon-600 dark:text-dragon-400">驱动支付业务</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">推广Shop Pay获取交易费收益</p>
                                </div>
                                <div class="border-l-4 border-festival-500 pl-4">
                                    <h4 class="font-semibold text-festival-600 dark:text-festival-400">生态网络效应</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">形成正向循环，提升整体竞争力</p>
                                </div>
                                <div class="border-l-4 border-purple-500 pl-4">
                                    <h4 class="font-semibold text-purple-600 dark:text-purple-400">对抗亚马逊</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">减少对外部流量依赖</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收费模式对比表 -->
                    <div class="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg mb-12">
                        <h3 class="text-xl font-semibold mb-6 text-center">Shop App 服务收费模式</h3>
                        <div class="responsive-table">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-3 px-4 font-semibold">服务</th>
                                        <th class="text-left py-3 px-4 font-semibold">费用类型</th>
                                        <th class="text-left py-3 px-4 font-semibold">成本 (美国示例)</th>
                                        <th class="text-left py-3 px-4 font-semibold">备注/条件</th>
                                    </tr>
                                </thead>
                                <tbody class="text-sm">
                                    <tr class="border-b border-gray-100 dark:border-gray-800">
                                        <td class="py-3 px-4">Shop Pay (标准结账)</td>
                                        <td class="py-3 px-4">交易费</td>
                                        <td class="py-3 px-4 text-green-600 dark:text-green-400">包含在 Shopify Payments 费率内</td>
                                        <td class="py-3 px-4">无额外费用</td>
                                    </tr>
                                    <tr class="border-b border-gray-100 dark:border-gray-800">
                                        <td class="py-3 px-4">Shop Pay Installments</td>
                                        <td class="py-3 px-4">交易费</td>
                                        <td class="py-3 px-4 text-yellow-600 dark:text-yellow-400">5.9% + $0.30/笔</td>
                                        <td class="py-3 px-4">仅针对分期付款功能</td>
                                    </tr>
                                    <tr class="border-b border-gray-100 dark:border-gray-800">
                                        <td class="py-3 px-4">Shop Campaigns</td>
                                        <td class="py-3 px-4">获客成本 (CAC)</td>
                                        <td class="py-3 px-4 text-blue-600 dark:text-blue-400">按转化付费</td>
                                        <td class="py-3 px-4">商家设定目标 ROAS</td>
                                    </tr>
                                    <tr>
                                        <td class="py-3 px-4">Shop App 使用</td>
                                        <td class="py-3 px-4">无直接费用</td>
                                        <td class="py-3 px-4 text-green-600 dark:text-green-400">免费</td>
                                        <td class="py-3 px-4">作为增值服务</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 流量获取策略 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div class="card-hover bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg">
                            <h3 class="text-xl font-semibold mb-6 flex items-center">
                                <i class="fas fa-rocket text-primary-500 mr-3"></i>
                                核心流量来源
                            </h3>
                            <div class="space-y-4">
                                <div class="flex items-start space-x-3">
                                    <div class="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">1</div>
                                    <div>
                                        <h4 class="font-semibold">Shopify 体系导流</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">商家结账页、感谢页面推荐</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <div class="bg-dragon-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">2</div>
                                    <div>
                                        <h4 class="font-semibold">邮件引导</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">订单确认/发货邮件追踪链接</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <div class="bg-festival-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">3</div>
                                    <div>
                                        <h4 class="font-semibold">Shop Pay 用户</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">支付用户自然引导至应用</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-hover bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg">
                            <h3 class="text-xl font-semibold mb-6 flex items-center">
                                <i class="fas fa-bullhorn text-dragon-500 mr-3"></i>
                                外部推广策略
                            </h3>
                            <div class="space-y-4">
                                <div class="flex items-start space-x-3">
                                    <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">A</div>
                                    <div>
                                        <h4 class="font-semibold">应用商店优化</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">ASO优化提升自然下载</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <div class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">B</div>
                                    <div>
                                        <h4 class="font-semibold">付费广告</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">精准投放获取目标用户</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <div class="bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">C</div>
                                    <div>
                                        <h4 class="font-semibold">品牌营销</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">品牌活动提升知名度</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- SWOT 分析 -->
            <section id="swot-analysis" class="py-16 px-4">
                <div class="container mx-auto">
                    <h2 class="section-title text-3xl md:text-4xl font-bold mb-8">第五部分：SWOT 分析</h2>
                    <p class="text-center text-gray-600 dark:text-gray-400 mb-12 text-lg">基于我方H5门户产品视角的竞争分析</p>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- 优势 (Strengths) -->
                        <div class="card-hover bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border-l-4 border-green-500">
                            <h3 class="text-xl font-semibold mb-6 flex items-center text-green-600 dark:text-green-400">
                                <i class="fas fa-thumbs-up mr-3"></i>
                                优势 (Strengths)
                            </h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">我们可以做得更好的地方</p>
                            <ul class="space-y-3">
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">灵活性与低门槛</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">H5形态，无需下载安装，降低使用门槛</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">社交分享便捷</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">易于通过社交媒体、即时通讯工具传播</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">专注性</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">聚焦特定垂直领域，做得更"小而美"</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">快速迭代</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">无需应用商店审核，快速部署更新</p>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <!-- 劣势 (Weaknesses) -->
                        <div class="card-hover bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border-l-4 border-red-500">
                            <h3 class="text-xl font-semibold mb-6 flex items-center text-red-600 dark:text-red-400">
                                <i class="fas fa-thumbs-down mr-3"></i>
                                劣势 (Weaknesses)
                            </h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">我们面临的挑战</p>
                            <ul class="space-y-3">
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-times-circle text-red-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">功能限制</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">H5在自动订单追踪、消息推送等方面存在短板</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-times-circle text-red-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">生态规模</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">初期商户和商品数量远小于Shopify</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-times-circle text-red-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">品牌信任</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">缺乏强大品牌背书，建立信任需要时间</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-times-circle text-red-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">支付便捷性差距</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">难以复制Shop Pay的网络效应</p>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <!-- 机会 (Opportunities) -->
                        <div class="card-hover bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border-l-4 border-blue-500">
                            <h3 class="text-xl font-semibold mb-6 flex items-center text-blue-600 dark:text-blue-400">
                                <i class="fas fa-lightbulb mr-3"></i>
                                机会 (Opportunities)
                            </h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">市场留给我们的空间</p>
                            <ul class="space-y-3">
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-star text-blue-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">垂直深耕</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">针对特定品类打造内容驱动的导购社区</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-star text-blue-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">商家服务</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">提供更深度的商家赋能和精准用户画像</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-star text-blue-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">内容与策展差异化</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">人工精选、主题策划打造独特调性</p>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <!-- 威胁 (Threats) -->
                        <div class="card-hover bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border-l-4 border-yellow-500">
                            <h3 class="text-xl font-semibold mb-6 flex items-center text-yellow-600 dark:text-yellow-400">
                                <i class="fas fa-exclamation-triangle mr-3"></i>
                                威胁 (Threats)
                            </h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">需要警惕的风险</p>
                            <ul class="space-y-3">
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-exclamation-circle text-yellow-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">巨头竞争</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Shopify或其他巨头可能推出类似产品</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-exclamation-circle text-yellow-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">用户心智</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">用户习惯使用原生App，对H5接受度不确定</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <i class="fas fa-exclamation-circle text-yellow-500 mt-1"></i>
                                    <div>
                                        <h4 class="font-semibold">商家忠诚度</h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">商家可能同时运营多平台，用户被分流</p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 战略建议与行动计划 -->
            <section id="strategic-recommendations" class="py-16 px-4 bg-gray-100 dark:bg-gray-800">
                <div class="container mx-auto">
                    <h2 class="section-title text-3xl md:text-4xl font-bold mb-8">第六部分：战略建议与行动计划</h2>

                    <!-- 产品定位 -->
                    <div class="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg mb-12">
                        <h3 class="text-2xl font-semibold mb-8 text-center flex items-center justify-center">
                            <i class="fas fa-crosshairs text-primary-500 mr-3"></i>
                            产品定位与差异化策略
                        </h3>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <div>
                                <h4 class="text-lg font-semibold mb-4 text-dragon-600 dark:text-dragon-400">市场定位建议</h4>
                                <div class="space-y-4">
                                    <div class="p-4 bg-dragon-50 dark:bg-dragon-900/20 rounded-lg">
                                        <h5 class="font-semibold text-dragon-700 dark:text-dragon-300">垂直领域内容导购平台</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">专注特定品类，提供深度内容和专业服务</p>
                                    </div>
                                    <div class="p-4 bg-festival-50 dark:bg-festival-900/20 rounded-lg">
                                        <h5 class="font-semibold text-festival-700 dark:text-festival-300">本地/区域精选购物门户</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">利用本土优势，提供贴近本地的服务</p>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold mb-4 text-festival-600 dark:text-festival-400">核心价值主张</h4>
                                <div class="p-6 bg-gradient-to-br from-dragon-100 to-festival-100 dark:from-dragon-900/20 dark:to-festival-900/20 rounded-lg">
                                    <blockquote class="text-lg font-medium text-center">
                                        "为您发现/购买 [特定领域] 最有趣的商品"
                                    </blockquote>
                                    <p class="text-center text-sm text-gray-600 dark:text-gray-400 mt-2">
                                        初期不与Shop App在"全网追踪"或"海量选择"上直接竞争
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- MVP 功能规划 -->
                    <div class="mb-12">
                        <h3 class="text-2xl font-semibold mb-8 text-center">MVP (最小可行产品) 功能规划</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- P0 优先级 -->
                            <div class="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg">
                                <div class="text-center mb-4">
                                    <div class="bg-red-500 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <span class="font-bold text-lg">P0</span>
                                    </div>
                                    <h4 class="text-lg font-semibold text-red-600 dark:text-red-400">核心必须</h4>
                                </div>
                                <ul class="space-y-2 text-sm">
                                    <li class="flex items-center"><i class="fas fa-circle text-red-500 mr-2 text-xs"></i>商家店铺和商品展示</li>
                                    <li class="flex items-center"><i class="fas fa-circle text-red-500 mr-2 text-xs"></i>平台内订单追踪</li>
                                    <li class="flex items-center"><i class="fas fa-circle text-red-500 mr-2 text-xs"></i>商品搜索功能</li>
                                    <li class="flex items-center"><i class="fas fa-circle text-red-500 mr-2 text-xs"></i>用户个人中心</li>
                                    <li class="flex items-center"><i class="fas fa-circle text-red-500 mr-2 text-xs"></i>支付引导/集成</li>
                                </ul>
                            </div>

                            <!-- P1 优先级 -->
                            <div class="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg">
                                <div class="text-center mb-4">
                                    <div class="bg-yellow-500 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <span class="font-bold text-lg">P1</span>
                                    </div>
                                    <h4 class="text-lg font-semibold text-yellow-600 dark:text-yellow-400">重要增强</h4>
                                </div>
                                <ul class="space-y-2 text-sm">
                                    <li class="flex items-center"><i class="fas fa-circle text-yellow-500 mr-2 text-xs"></i>基础个性化推荐</li>
                                    <li class="flex items-center"><i class="fas fa-circle text-yellow-500 mr-2 text-xs"></i>内容专题/编辑精选</li>
                                    <li class="flex items-center"><i class="fas fa-circle text-yellow-500 mr-2 text-xs"></i>店铺关注功能</li>
                                    <li class="flex items-center"><i class="fas fa-circle text-yellow-500 mr-2 text-xs"></i>评论与评分</li>
                                </ul>
                            </div>

                            <!-- 暂不考虑 -->
                            <div class="bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg">
                                <div class="text-center mb-4">
                                    <div class="bg-gray-500 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <span class="font-bold text-lg">P2</span>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-600 dark:text-gray-400">未来考虑</h4>
                                </div>
                                <ul class="space-y-2 text-sm">
                                    <li class="flex items-center"><i class="fas fa-circle text-gray-500 mr-2 text-xs"></i>跨平台自动订单追踪</li>
                                    <li class="flex items-center"><i class="fas fa-circle text-gray-500 mr-2 text-xs"></i>自建支付体系</li>
                                    <li class="flex items-center"><i class="fas fa-circle text-gray-500 mr-2 text-xs"></i>复杂AI购物助手</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 关键挑战应对策略 -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                        <div class="card-hover bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg">
                            <h4 class="text-lg font-semibold mb-4 flex items-center">
                                <i class="fas fa-truck text-primary-500 mr-3"></i>
                                追踪功能局限
                            </h4>
                            <div class="space-y-3 text-sm">
                                <div class="p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
                                    <h5 class="font-semibold text-primary-700 dark:text-primary-300">策略</h5>
                                    <p class="text-gray-600 dark:text-gray-400">强化平台内订单追踪体验，做到极致清晰、及时</p>
                                </div>
                                <div class="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                    <h5 class="font-semibold text-blue-700 dark:text-blue-300">补充</h5>
                                    <p class="text-gray-600 dark:text-gray-400">提供便捷的手动添加外部订单方式</p>
                                </div>
                            </div>
                        </div>

                        <div class="card-hover bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg">
                            <h4 class="text-lg font-semibold mb-4 flex items-center">
                                <i class="fas fa-bell text-dragon-500 mr-3"></i>
                                用户触达优化
                            </h4>
                            <div class="space-y-3 text-sm">
                                <div class="p-3 bg-dragon-50 dark:bg-dragon-900/20 rounded-lg">
                                    <h5 class="font-semibold text-dragon-700 dark:text-dragon-300">Email & SMS</h5>
                                    <p class="text-gray-600 dark:text-gray-400">重点利用邮件和短信进行订单状态更新</p>
                                </div>
                                <div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                    <h5 class="font-semibold text-yellow-700 dark:text-yellow-300">PWA优化</h5>
                                    <p class="text-gray-600 dark:text-gray-400">充分利用PWA的Web Push能力</p>
                                </div>
                            </div>
                        </div>

                        <div class="card-hover bg-white dark:bg-gray-900 rounded-xl p-6 shadow-lg">
                            <h4 class="text-lg font-semibold mb-4 flex items-center">
                                <i class="fas fa-tachometer-alt text-festival-500 mr-3"></i>
                                性能与流畅度
                            </h4>
                            <div class="space-y-3 text-sm">
                                <div class="p-3 bg-festival-50 dark:bg-festival-900/20 rounded-lg">
                                    <h5 class="font-semibold text-festival-700 dark:text-festival-300">前端优化</h5>
                                    <p class="text-gray-600 dark:text-gray-400">极致的前端性能优化，确保快速加载</p>
                                </div>
                                <div class="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                    <h5 class="font-semibold text-green-700 dark:text-green-300">PWA加持</h5>
                                    <p class="text-gray-600 dark:text-gray-400">Service Worker缓存实现离线访问</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 推广与冷启动策略 -->
                    <div class="bg-gradient-to-r from-dragon-50 to-festival-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-8">
                        <h3 class="text-2xl font-semibold mb-8 text-center">推广与冷启动策略</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div>
                                <h4 class="text-lg font-semibold mb-4 flex items-center">
                                    <i class="fas fa-store text-dragon-500 mr-3"></i>
                                    种子商家策略
                                </h4>
                                <div class="space-y-3">
                                    <div class="flex items-start space-x-3">
                                        <div class="bg-dragon-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">1</div>
                                        <div>
                                            <h5 class="font-semibold">优先邀请</h5>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">SaaS平台内运营良好的商家首批入驻</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start space-x-3">
                                        <div class="bg-festival-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">2</div>
                                        <div>
                                            <h5 class="font-semibold">资源倾斜</h5>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">提供首页推荐位和运营指导</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold mb-4 flex items-center">
                                    <i class="fas fa-users text-festival-500 mr-3"></i>
                                    种子用户策略
                                </h4>
                                <div class="space-y-3">
                                    <div class="flex items-start space-x-3">
                                        <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">A</div>
                                        <div>
                                            <h5 class="font-semibold">私域流量</h5>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">利用现有商家的社群、邮件等宣传</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start space-x-3">
                                        <div class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mt-1">B</div>
                                        <div>
                                            <h5 class="font-semibold">订单引导</h5>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">在订单确认页设置H5门户入口</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 数据可视化图表 -->
            <section class="py-16 px-4">
                <div class="container mx-auto">
                    <h2 class="section-title text-3xl md:text-4xl font-bold mb-8 text-center">关键数据洞察</h2>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Shop App vs 我方产品对比 -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                            <h3 class="text-xl font-semibold mb-6 text-center">功能对比分析</h3>
                            <div class="chart-container">
                                <canvas id="comparisonChart"></canvas>
                            </div>
                        </div>

                        <!-- 市场机会分布 -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                            <h3 class="text-xl font-semibold mb-6 text-center">市场机会分布</h3>
                            <div class="chart-container">
                                <canvas id="opportunityChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 回到顶部按钮 -->
    <button id="back-to-top" class="fixed bottom-8 right-8 bg-dragon-500 hover:bg-dragon-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 opacity-0 invisible">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- 页脚 -->
    <footer class="bg-white dark:bg-gray-800 shadow-inner py-12 border-t border-dragon-200 dark:border-gray-700">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <i class="fas fa-dragon text-dragon-500 text-2xl"></i>
                        <h3 class="text-lg font-semibold">竞品调研报告</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">
                        深度分析Shopify Shop App，为H5购物门户产品提供战略指导
                    </p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">快速导航</h4>
                    <ul class="space-y-2 text-sm">
                        <li><a href="#executive-summary" class="text-gray-600 dark:text-gray-400 hover:text-dragon-500 transition-colors">报告概述</a></li>
                        <li><a href="#product-teardown" class="text-gray-600 dark:text-gray-400 hover:text-dragon-500 transition-colors">产品拆解</a></li>
                        <li><a href="#swot-analysis" class="text-gray-600 dark:text-gray-400 hover:text-dragon-500 transition-colors">SWOT分析</a></li>
                        <li><a href="#strategic-recommendations" class="text-gray-600 dark:text-gray-400 hover:text-dragon-500 transition-colors">战略建议</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">联系信息</h4>
                    <div class="flex space-x-4">
                        <a href="https://github.com/caiwenhao" class="text-gray-600 dark:text-gray-400 hover:text-dragon-500 transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="https://twitter.com/caiwenhao" class="text-gray-600 dark:text-gray-400 hover:text-dragon-500 transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="https://linkedin.com/in/caiwenhao" class="text-gray-600 dark:text-gray-400 hover:text-dragon-500 transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                    <div class="mt-4">
                        <a href="index.html" class="inline-flex items-center space-x-2 text-dragon-500 hover:text-dragon-600 transition-colors">
                            <i class="fas fa-home"></i>
                            <span>返回首页</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-600 dark:text-gray-400">&copy; 2025 竞品调研报告. 保留所有权利。</p>
                <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">作者：蔡文浩 | 端午赛龙舟主题设计</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 深色/浅色模式切换
        const themeToggle = document.getElementById('theme-toggle');
        const html = document.documentElement;

        // 检查系统偏好
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            html.classList.add('dark');
        }

        // 检查本地存储中的主题设置
        if (localStorage.getItem('theme') === 'dark') {
            html.classList.add('dark');
        } else if (localStorage.getItem('theme') === 'light') {
            html.classList.remove('dark');
        }

        // 主题切换按钮点击事件
        themeToggle.addEventListener('click', () => {
            if (html.classList.contains('dark')) {
                html.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }
        });

        // 侧边栏切换
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebar-overlay');

        function toggleSidebar() {
            const isHidden = sidebar.classList.contains('hidden');

            if (isHidden) {
                sidebar.classList.remove('hidden');
                if (window.innerWidth < 768) {
                    sidebarOverlay.classList.add('show');
                }
            } else {
                sidebar.classList.add('hidden');
                sidebarOverlay.classList.remove('show');
            }
        }

        function closeSidebar() {
            sidebar.classList.add('hidden');
            sidebarOverlay.classList.remove('show');
        }

        sidebarToggle.addEventListener('click', toggleSidebar);

        // 点击遮罩关闭侧边栏
        sidebarOverlay.addEventListener('click', closeSidebar);

        // 点击侧边栏外部关闭侧边栏（移动端）
        document.addEventListener('click', (e) => {
            if (window.innerWidth < 768 &&
                !sidebar.contains(e.target) &&
                !sidebarToggle.contains(e.target) &&
                !sidebarOverlay.contains(e.target)) {
                closeSidebar();
            }
        });

        // 窗口大小改变时处理侧边栏
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 768) {
                // 桌面端：显示侧边栏，隐藏遮罩
                sidebar.classList.remove('hidden');
                sidebarOverlay.classList.remove('show');
            } else {
                // 移动端：隐藏侧边栏
                sidebar.classList.add('hidden');
                sidebarOverlay.classList.remove('show');
            }
        });

        // 初始化侧边栏状态
        if (window.innerWidth < 768) {
            sidebar.classList.add('hidden');
            sidebarOverlay.classList.remove('show');
        } else {
            sidebar.classList.remove('hidden');
            sidebarOverlay.classList.remove('show');
        }

        // 滚动进度条
        const progressBar = document.getElementById('progressBar');

        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.offsetHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            progressBar.style.width = scrollPercent + '%';
        });

        // 回到顶部按钮
        const backToTopBtn = document.getElementById('back-to-top');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.remove('opacity-0', 'invisible');
                backToTopBtn.classList.add('opacity-100', 'visible');
            } else {
                backToTopBtn.classList.add('opacity-0', 'invisible');
                backToTopBtn.classList.remove('opacity-100', 'visible');
            }
        });

        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 导航链接高亮
        const navLinks = document.querySelectorAll('.nav-link');
        const sections = document.querySelectorAll('section[id]');

        window.addEventListener('scroll', () => {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('bg-dragon-100', 'dark:bg-gray-600', 'text-dragon-700', 'dark:text-dragon-300');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('bg-dragon-100', 'dark:bg-gray-600', 'text-dragon-700', 'dark:text-dragon-300');
                }
            });
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', () => {
            // 添加淡入动画
            const animatedElements = document.querySelectorAll('.animate-fade-in, .animate-slide-up');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1
            });

            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });

            // 初始化图表
            initCharts();
        });

        // 图表初始化
        function initCharts() {
            // 功能对比雷达图
            const comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
            new Chart(comparisonCtx, {
                type: 'radar',
                data: {
                    labels: ['订单追踪', '商品发现', '支付便捷', '用户体验', '商家服务', '技术门槛'],
                    datasets: [{
                        label: 'Shop App',
                        data: [95, 85, 95, 90, 80, 70],
                        borderColor: '#0ea5e9',
                        backgroundColor: 'rgba(14, 165, 233, 0.2)',
                        pointBackgroundColor: '#0ea5e9',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: '#0ea5e9'
                    }, {
                        label: '我方H5门户',
                        data: [70, 90, 75, 85, 90, 95],
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.2)',
                        pointBackgroundColor: '#f59e0b',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: '#f59e0b'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                stepSize: 20,
                                backdropColor: 'transparent'
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    }
                }
            });

            // 市场机会饼图
            const opportunityCtx = document.getElementById('opportunityChart').getContext('2d');
            new Chart(opportunityCtx, {
                type: 'doughnut',
                data: {
                    labels: ['垂直深耕', '内容策展', '本土化服务', 'PWA优势', '快速迭代'],
                    datasets: [{
                        data: [30, 25, 20, 15, 10],
                        backgroundColor: [
                            '#0ea5e9',
                            '#f59e0b',
                            '#22c55e',
                            '#8b5cf6',
                            '#ef4444'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    },
                    cutout: '40%'
                }
            });
        }
    </script>
</body>
</html>