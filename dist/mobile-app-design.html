<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动应用设计工作流 - 无需Figma的客户端应用开发</title>
    <!-- TailwindCSS 通过CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 字体 Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        // 配置Tailwind主题
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f7ff',
                            100: '#e0efff',
                            200: '#bae0ff',
                            300: '#7cc5ff',
                            400: '#36a3ff',
                            500: '#0087ff',
                            600: '#0068df',
                            700: '#0057b8',
                            800: '#004897',
                            900: '#00397c',
                        },
                        secondary: {
                            50: '#f5f7fa',
                            100: '#ebeef5',
                            200: '#dde2ee',
                            300: '#c5cfe0',
                            400: '#a7b4cc',
                            500: '#8c9ab8',
                            600: '#7382a5',
                            700: '#5f6c8f',
                            800: '#4f5a77',
                            900: '#404a61',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                },
            },
        }
    </script>
    <style type="text/css">
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        /* 自定义样式 */
        .step-card {
            transition: all 0.3s ease;
        }
        
        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 加载动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease forwards;
        }
        
        /* 确保深色模式下的文本可读性 */
        .dark .dark:text-white {
            color: #f3f4f6;
        }
    </style>
</head>
<body class="font-sans bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 检测系统主题并设置初始主题 -->
    <script>
        // 检查用户偏好
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    </script>

    <!-- 导航栏 -->
    <nav class="sticky top-0 z-50 bg-white dark:bg-gray-800 shadow-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="#" class="flex-shrink-0 flex items-center">
                        <i class="fas fa-mobile-alt text-primary-600 dark:text-primary-400 text-2xl mr-2"></i>
                        <span class="font-bold text-xl">移动应用设计工作流</span>
                    </a>
                </div>
                <div class="flex items-center">
                    <!-- 主题切换按钮 -->
                    <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none">
                        <i id="theme-icon" class="fas fa-moon text-gray-600 dark:text-gray-300"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <header class="bg-gradient-to-r from-primary-600 to-blue-700 dark:from-primary-800 dark:to-blue-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in">
                如何为客户设计移动应用
            </h1>
            <p class="text-xl text-white opacity-90 max-w-3xl mx-auto animate-fade-in" style="animation-delay: 0.2s">
                无需Figma，无需设计团队，无需浪费数周时间
            </p>
            <div class="mt-6 animate-fade-in" style="animation-delay: 0.3s">
                <p class="text-lg text-white opacity-80 max-w-2xl mx-auto">
                    只需使用UX Pilot、Lovable、Supabase和Cursor，即可快速构建专业应用
                </p>
            </div>
            <div class="mt-10 animate-fade-in" style="animation-delay: 0.4s">
                <a href="#workflow" class="bg-white text-primary-600 hover:bg-gray-100 font-semibold px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition duration-300 inline-flex items-center">
                    <i class="fas fa-arrow-down mr-2"></i>
                    查看详细步骤
                </a>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <!-- 介绍 -->
        <section id="intro" class="mb-20 animate-fade-in">
            <div class="text-center max-w-3xl mx-auto">
                <h2 class="text-3xl font-bold mb-6 text-gray-800 dark:text-white">MoodFlow应用案例</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 mb-8">
                    以下是Prajwal Tomar一步步设计MoodFlow（一款AI日记应用）的完整工作流程。这种方法可以帮助你快速高效地为客户开发移动应用，无需传统的设计工具和团队。
                </p>
                <div class="flex justify-center">
                    <img src="https://images.unsplash.com/photo-1551650975-87deedd944c3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" alt="移动应用设计" class="rounded-xl shadow-lg max-w-full h-auto" loading="lazy">
                </div>
            </div>
        </section>

        <!-- 工作流程部分 -->
        <section id="workflow" class="mb-20">
            <h2 class="text-3xl font-bold mb-10 text-center text-gray-800 dark:text-white">详细工作流程</h2>
            
            <!-- 步骤 1 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="1">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">1</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">从明确的想法开始</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            在接触任何工具之前，我先明确核心问题、解决方案和目标用户。这为整个构建过程奠定基础。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">案例说明：</h4>
                            <p class="text-gray-600 dark:text-gray-300">
                                在这个案例中，我们要构建的是一个名为MoodFlow的AI日记应用。
                            </p>
                        </div>
                        
                        <div class="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4 border-l-4 border-blue-500">
                            <p class="text-blue-700 dark:text-blue-300 font-medium">
                                <i class="fas fa-lightbulb mr-2"></i>提示：清晰的产品定位可以帮助你避免后期的返工和混乱，这是最容易被忽视但最重要的一步。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 2 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="2">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">2</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">深入了解产品愿景</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            这一步是关于比客户更好地理解MVP（最小可行产品）。我分解用户旅程，定义关键痛点，并规划应用将如何解决这些问题。
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-map-signs text-orange-500 mr-2"></i>
                                    <h4 class="font-semibold text-gray-700 dark:text-gray-200">用户旅程</h4>
                                </div>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">详细规划用户如何与应用交互的每一步</p>
                            </div>
                            
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                                    <h4 class="font-semibold text-gray-700 dark:text-gray-200">关键痛点</h4>
                                </div>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">识别用户面临的主要问题和挑战</p>
                            </div>
                            
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-puzzle-piece text-green-500 mr-2"></i>
                                    <h4 class="font-semibold text-gray-700 dark:text-gray-200">解决方案映射</h4>
                                </div>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">明确应用功能如何解决这些痛点</p>
                            </div>
                        </div>
                        
                        <div class="bg-green-50 dark:bg-green-900/30 rounded-lg p-4 border-l-4 border-green-500">
                            <p class="text-green-700 dark:text-green-300">
                                <i class="fas fa-check-circle mr-2"></i>这一步可以节省后期的大量时间，使开发过程更加顺畅。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 3 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="3">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">3</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">为什么选择PWA？</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            我将这个应用构建为PWA（渐进式Web应用）而不是原生应用。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">PWA的优势：</h4>
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>一套代码同时适用于移动端和桌面端</li>
                                <li>无需等待应用商店审核</li>
                                <li>易于测试、迭代和发布</li>
                            </ul>
                        </div>
                        
                        <div class="bg-indigo-50 dark:bg-indigo-900/30 rounded-lg p-4 border-l-4 border-indigo-500">
                            <p class="text-indigo-700 dark:text-indigo-300">
                                <i class="fas fa-info-circle mr-2"></i>对于早期阶段的产品来说，PWA更快速、更精简。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 4 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="4">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">4</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">优先考虑核心功能</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            现在我精简范围。目标是将MVP限制在5个主要功能以内。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">通常包括：</h4>
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>身份验证</li>
                                <li>Stripe支付（如需要）</li>
                                <li>核心用户流程</li>
                            </ul>
                        </div>
                        
                        <div class="bg-yellow-50 dark:bg-yellow-900/30 rounded-lg p-4 border-l-4 border-yellow-500">
                            <p class="text-yellow-700 dark:text-yellow-300">
                                <i class="fas fa-exclamation-triangle mr-2"></i>范围越紧凑，发布速度越快。不要被诱惑添加过多功能！
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 5 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="5">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">5</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">起草UI开发计划</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            在这个阶段，我让ChatGPT列出PWA所需的所有界面，并附上详细描述。这成为我的UI蓝图。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">提示示例：</h4>
                            <div class="bg-gray-100 dark:bg-gray-600 p-3 rounded-md text-sm font-mono">
                                "列出构建[应用名称]所需的所有界面，包括每个界面的详细功能描述、UI元素和交互逻辑。"
                            </div>
                        </div>
                        
                        <div class="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-4 border-l-4 border-purple-500 mt-4">
                            <p class="text-purple-700 dark:text-purple-300">
                                <i class="fas fa-sitemap mr-2"></i>你也可以将此转换为站点地图，与客户分享以获得快速批准。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 6 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="6">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">6</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">在UX Pilot中生成UI</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            使用界面描述，我将布局输入到UX Pilot中。几秒钟内，它就会生成干净、高保真的UI。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">UX Pilot优势：</h4>
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>快速生成高保真UI</li>
                                <li>可以调整字体、结构</li>
                                <li>可以导出代码或截图，随时可用</li>
                            </ul>
                        </div>
                        
                        <div class="flex justify-center my-4">
                            <img src="https://images.unsplash.com/photo-1551650992-ee4fd47df41f?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="UX Pilot界面设计" class="rounded-lg shadow-md max-w-full h-auto" loading="lazy">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 7 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="7">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">7</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">在Lovable中组装一切</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            UI完成后，我转到Lovable。在这里我：
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>上传项目简介+UX Pilot的代码</li>
                                <li>规划导航和逻辑</li>
                                <li>连接真实的前端状态+交互</li>
                            </ul>
                        </div>
                        
                        <div class="bg-indigo-50 dark:bg-indigo-900/30 rounded-lg p-4 border-l-4 border-indigo-500">
                            <p class="text-indigo-700 dark:text-indigo-300">
                                <i class="fas fa-magic mr-2"></i>几小时内，它就变成了一个功能齐全的PWA。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 8 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="8">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">8</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">通过Supabase构建后端</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Lovable原生支持Supabase。
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <h4 class="font-semibold mb-2 text-gray-700 dark:text-gray-200">Supabase提供：</h4>
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>身份验证（电子邮件+魔法链接）</li>
                                <li>数据库（日记、情绪、反思）</li>
                                <li>用于AI逻辑的Edge Functions</li>
                                <li>语音笔记的安全存储</li>
                            </ul>
                        </div>
                        
                        <div class="bg-green-50 dark:bg-green-900/30 rounded-lg p-4 border-l-4 border-green-500">
                            <p class="text-green-700 dark:text-green-300">
                                <i class="fas fa-server mr-2"></i>无需DevOps，无需设置烦恼。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 9 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="9">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">9</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">高级逻辑？使用Cursor</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            这次构建不需要，但如果需要，Cursor可以让我：
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>用AI编辑模式</li>
                                <li>生成迁移</li>
                                <li>添加RLS策略</li>
                                <li>优化查询</li>
                            </ul>
                        </div>
                        
                        <div class="bg-purple-50 dark:bg-purple-900/30 rounded-lg p-4 border-l-4 border-purple-500">
                            <p class="text-purple-700 dark:text-purple-300">
                                <i class="fas fa-code-branch mr-2"></i>当你需要更多控制时，这是完美的选择。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 10 -->
            <div class="step-card bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8 opacity-0 transform translate-y-4" data-step="10">
                <div class="flex items-start">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3 mr-4">
                        <span class="flex items-center justify-center h-8 w-8 text-primary-600 dark:text-primary-300 font-bold text-xl">10</span>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">启动和部署</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            最后一步：
                        </p>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                            <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-1">
                                <li>在Lovable中预览</li>
                                <li>通过Netlify连接自定义域名</li>
                                <li>添加PWA清单，使其像真正的应用一样安装</li>
                            </ul>
                        </div>
                        
                        <div class="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4 border-l-4 border-blue-500">
                            <p class="text-blue-700 dark:text-blue-300">
                                <i class="fas fa-rocket mr-2"></i>就这样，它已经上线、运行并准备好供用户使用。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- 总结部分 -->
    <section id="summary" class="mb-20">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-8 shadow-md">
            <h2 class="text-2xl font-bold mb-6 text-center text-gray-800 dark:text-white">关键要点</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white dark:bg-gray-800 p-5 rounded-lg shadow-sm">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-lightbulb text-yellow-500 text-xl mr-3"></i>
                        <h3 class="font-semibold text-lg">无需设计团队</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">
                        使用现代AI工具，单人开发者也能创建专业级移动应用，无需传统设计团队和繁琐流程。
                    </p>
                </div>
                
                <div class="bg-white dark:bg-gray-800 p-5 rounded-lg shadow-sm">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-rocket text-blue-500 text-xl mr-3"></i>
                        <h3 class="font-semibold text-lg">快速迭代</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">
                        PWA方案让你能够快速迭代和发布，无需等待应用商店审核，大大缩短开发周期。
                    </p>
                </div>
                
                <div class="bg-white dark:bg-gray-800 p-5 rounded-lg shadow-sm">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-tools text-green-500 text-xl mr-3"></i>
                        <h3 class="font-semibold text-lg">工具协同</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">
                        UX Pilot、Lovable、Supabase和Cursor的协同工作流程，让应用开发变得简单高效。
                    </p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 工具箱部分 -->
    <section id="toolbox" class="mb-20">
        <h2 class="text-3xl font-bold mb-10 text-center text-gray-800 dark:text-white">推荐工具箱</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center justify-center mb-4">
                    <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                        <i class="fas fa-paint-brush text-primary-600 dark:text-primary-300 text-2xl"></i>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-center mb-2 text-gray-800 dark:text-white">UX Pilot</h3>
                <p class="text-gray-600 dark:text-gray-300 text-center text-sm">
                    AI驱动的界面设计工具，几秒钟内生成高保真UI
                </p>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center justify-center mb-4">
                    <div class="w-16 h-16 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
                        <i class="fas fa-magic text-indigo-600 dark:text-indigo-300 text-2xl"></i>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-center mb-2 text-gray-800 dark:text-white">Lovable</h3>
                <p class="text-gray-600 dark:text-gray-300 text-center text-sm">
                    将UI设计转化为功能齐全的PWA应用
                </p>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center justify-center mb-4">
                    <div class="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                        <i class="fas fa-database text-green-600 dark:text-green-300 text-2xl"></i>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-center mb-2 text-gray-800 dark:text-white">Supabase</h3>
                <p class="text-gray-600 dark:text-gray-300 text-center text-sm">
                    开源的Firebase替代品，提供数据库、身份验证和存储
                </p>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center justify-center mb-4">
                    <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                        <i class="fas fa-code text-purple-600 dark:text-purple-300 text-2xl"></i>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-center mb-2 text-gray-800 dark:text-white">Cursor</h3>
                <p class="text-gray-600 dark:text-gray-300 text-center text-sm">
                    AI增强的代码编辑器，用于高级逻辑和优化
                </p>
            </div>
        </div>
    </section>
    
    <!-- 页脚 -->
    <footer class="bg-gray-100 dark:bg-gray-800 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-6 md:mb-0">
                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">移动应用设计工作流</h2>
                    <p class="text-gray-600 dark:text-gray-300">
                        无需Figma，无需设计团队，快速构建专业应用
                    </p>
                </div>
                
                <div class="flex flex-col">
                    <h3 class="text-lg font-semibold mb-3 text-gray-800 dark:text-white">作者信息</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-2">作者姓名: 蔡文浩</p>
                    <div class="flex space-x-4">
                        <a href="https://github.com/caiwenhao" target="_blank" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="https://twitter.com/caiwenhao" target="_blank" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="https://linkedin.com/in/caiwenhao" target="_blank" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-500 dark:text-gray-400">
                    &copy; 2025 蔡文浩. 保留所有权利.
                </p>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script>
        // 主题切换功能
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = document.getElementById('theme-icon');
        
        themeToggle.addEventListener('click', function() {
            // 切换深色模式
            document.documentElement.classList.toggle('dark');
            
            // 更新图标
            if (document.documentElement.classList.contains('dark')) {
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                localStorage.theme = 'dark';
            } else {
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
                localStorage.theme = 'light';
            }
        });
        
        // 初始化图标状态
        if (document.documentElement.classList.contains('dark')) {
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        }
        
        // 滚动动画
        function checkScroll() {
            const stepCards = document.querySelectorAll('.step-card');
            
            stepCards.forEach(card => {
                const cardTop = card.getBoundingClientRect().top;
                const windowHeight = window.innerHeight;
                
                if (cardTop < windowHeight * 0.85) {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                }
            });
        }
        
        // 初始检查
        window.addEventListener('load', checkScroll);
        // 滚动时检查
        window.addEventListener('scroll', checkScroll);
    </script>
</body>
</html>
