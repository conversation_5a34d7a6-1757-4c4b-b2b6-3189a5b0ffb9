<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动应用设计工作流 - <PERSON><PERSON><PERSON>方法</title>
    <!-- TailwindCSS 通过CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        // 配置Tailwind主题
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                    },
                }
            }
        }
    </script>
    <style type="text/css">
        /* 自定义样式 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 加载动画 */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        .slide-up {
            animation: slideUp 0.5s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        /* 步骤卡片样式 */
        .step-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        
        .step-card:hover {
            border-left-color: #0ea5e9;
        }
        
        /* 工具标签样式 */
        .tool-tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.25rem;
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 顶部导航栏 -->
    <header class="sticky top-0 z-50 bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fas fa-mobile-screen-button text-primary-600 dark:text-primary-400 text-2xl"></i>
                <h1 class="text-xl font-bold">移动应用设计工作流</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="index.html" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                    <i class="fas fa-home mr-1"></i> 首页
                </a>
                <!-- 深色/浅色模式切换按钮 -->
                <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-moon hidden dark:block text-yellow-300"></i>
                    <i class="fas fa-sun block dark:hidden text-yellow-500"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-4 py-8">
        <!-- 介绍部分 -->
        <section class="mb-12 fade-in">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">Prajwal Tomar的移动应用设计工作流</h2>
                <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
                    无需Figma、设计团队或浪费数周时间，仅使用UX Pilot、Lovable、Supabase和Cursor即可快速设计和开发移动应用
                </p>
                <div class="flex flex-wrap justify-center gap-4 mb-8">
                    <span class="tool-tag bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                        <i class="fas fa-wand-magic-sparkles mr-1"></i> UX Pilot
                    </span>
                    <span class="tool-tag bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200">
                        <i class="fas fa-heart mr-1"></i> Lovable
                    </span>
                    <span class="tool-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        <i class="fas fa-database mr-1"></i> Supabase
                    </span>
                    <span class="tool-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        <i class="fas fa-code mr-1"></i> Cursor
                    </span>
                </div>
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-1 rounded-xl shadow-lg">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6">
                        <p class="text-lg font-medium">
                            "这是我为客户设计移动应用的方式 - 无需Figma、无需设计团队、无需浪费数周时间。"
                        </p>
                        <p class="mt-4 text-right text-gray-600 dark:text-gray-400">— Prajwal Tomar</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 工作流程步骤 -->
        <section class="mb-12">
            <div class="max-w-4xl mx-auto">
                <h3 class="text-2xl font-bold mb-6 text-center">10步设计流程</h3>
                
                <!-- 步骤1 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 step-card slide-up" style="animation-delay: 0.1s;">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3 mr-4">
                            <span class="text-xl font-bold text-blue-600 dark:text-blue-300">1</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-semibold mb-2">从清晰的想法开始</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                在使用任何工具之前，先明确核心问题、解决方案和目标用户。这为整个构建过程奠定基础。
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <p class="text-sm">
                                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                                    <strong>案例应用:</strong> MoodFlow - 一款AI日记应用
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤2 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 step-card slide-up" style="animation-delay: 0.2s;">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3 mr-4">
                            <span class="text-xl font-bold text-blue-600 dark:text-blue-300">2</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-semibold mb-2">深入研究产品愿景</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                这一步是深入理解MVP，比客户更了解产品。分解用户旅程，定义关键痛点，并规划应用如何解决这些问题。
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <p class="text-sm">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <strong>好处:</strong> 节省后续时间并使开发更顺畅
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤3 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 step-card slide-up" style="animation-delay: 0.3s;">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3 mr-4">
                            <span class="text-xl font-bold text-blue-600 dark:text-blue-300">3</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-semibold mb-2">为什么选择PWA？</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                选择将应用构建为PWA（渐进式Web应用），而不是原生应用。
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-3">
                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                        <span>一个代码库支持移动端和桌面端</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                        <span>无需应用商店审核延迟</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                        <span>易于测试、迭代和发布</span>
                                    </li>
                                </ul>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                对于早期阶段的产品来说，这是更快、更精简的选择。
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤4 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 step-card slide-up" style="animation-delay: 0.4s;">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3 mr-4">
                            <span class="text-xl font-bold text-blue-600 dark:text-blue-300">4</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-semibold mb-2">优先考虑核心功能</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                精简项目范围，将MVP控制在5个主要功能以内。
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-3">
                                <p class="mb-2"><strong>通常包括：</strong></p>
                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <i class="fas fa-key text-yellow-500 mt-1 mr-2"></i>
                                        <span>身份验证</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-credit-card text-green-500 mt-1 mr-2"></i>
                                        <span>Stripe（如果需要）</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-route text-blue-500 mt-1 mr-2"></i>
                                        <span>核心用户流程</span>
                                    </li>
                                </ul>
                            </div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                <i class="fas fa-bolt text-yellow-500 mr-1"></i>
                                功能范围越小，交付速度越快。
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤5 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 step-card slide-up" style="animation-delay: 0.5s;">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3 mr-4">
                            <span class="text-xl font-bold text-blue-600 dark:text-blue-300">5</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-semibold mb-2">制定UI开发计划</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                在这一阶段，使用ChatGPT列出PWA所需的所有屏幕及其详细描述，形成UI蓝图。
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <p class="text-sm">
                                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                                    <strong>提示:</strong> 可以将其转化为站点地图与客户分享，以快速获得批准。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤6 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 step-card slide-up" style="animation-delay: 0.6s;">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3 mr-4">
                            <span class="text-xl font-bold text-blue-600 dark:text-blue-300">6</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-semibold mb-2">在UX Pilot中生成UI</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                使用屏幕描述，将布局输入UX Pilot。几秒钟内即可生成干净的高保真UI。
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <p class="text-sm">
                                    <i class="fas fa-sliders text-purple-500 mr-2"></i>
                                    <strong>功能:</strong> 可以调整字体、结构，并导出代码或截图，准备开始构建。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤7 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 step-card slide-up" style="animation-delay: 0.7s;">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3 mr-4">
                            <span class="text-xl font-bold text-blue-600 dark:text-blue-300">7</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-semibold mb-2">在Lovable中组装一切</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                UI完成后，转到Lovable进行组装。
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-3">
                                <p class="mb-2"><strong>在这里可以：</strong></p>
                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <i class="fas fa-upload text-pink-500 mt-1 mr-2"></i>
                                        <span>上传项目简介和UX Pilot的代码</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-sitemap text-pink-500 mt-1 mr-2"></i>
                                        <span>规划导航和逻辑</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-link text-pink-500 mt-1 mr-2"></i>
                                        <span>连接前端状态和交互</span>
                                    </li>
                                </ul>
                            </div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                几小时内即可完成一个功能齐全的PWA。
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤8 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 step-card slide-up" style="animation-delay: 0.8s;">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3 mr-4">
                            <span class="text-xl font-bold text-blue-600 dark:text-blue-300">8</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-semibold mb-2">通过Supabase实现后端</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                Lovable原生支持Supabase，可以快速实现后端功能。
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-3">
                                <p class="mb-2"><strong>Supabase提供：</strong></p>
                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <i class="fas fa-user-lock text-green-500 mt-1 mr-2"></i>
                                        <span>认证（电子邮件 + 魔法链接）</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-database text-green-500 mt-1 mr-2"></i>
                                        <span>数据库（日记、情绪、反思）</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-code text-green-500 mt-1 mr-2"></i>
                                        <span>Edge Functions用于AI逻辑</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-shield-alt text-green-500 mt-1 mr-2"></i>
                                        <span>安全存储用于语音笔记</span>
                                    </li>
                                </ul>
                            </div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                <i class="fas fa-check-circle text-green-500 mr-1"></i>
                                无需DevOps，无需复杂的设置。
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤9 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 step-card slide-up" style="animation-delay: 0.9s;">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3 mr-4">
                            <span class="text-xl font-bold text-blue-600 dark:text-blue-300">9</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-semibold mb-2">需要高级逻辑？使用Cursor</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                虽然MoodFlow没有用到，但如果需要更复杂的逻辑，可以使用Cursor。
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-3">
                                <p class="mb-2"><strong>Cursor支持：</strong></p>
                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <i class="fas fa-edit text-blue-500 mt-1 mr-2"></i>
                                        <span>使用AI编辑模式</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-code-branch text-blue-500 mt-1 mr-2"></i>
                                        <span>生成迁移</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-lock text-blue-500 mt-1 mr-2"></i>
                                        <span>添加RLS策略</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-tachometer-alt text-blue-500 mt-1 mr-2"></i>
                                        <span>优化查询</span>
                                    </li>
                                </ul>
                            </div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                适合需要更多控制的情况。
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- 步骤10 -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6 step-card slide-up" style="animation-delay: 1s;">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3 mr-4">
                            <span class="text-xl font-bold text-blue-600 dark:text-blue-300">10</span>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-semibold mb-2">启动和部署</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                最后一步是将应用部署上线。
                            </p>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-3">
                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <i class="fas fa-eye text-indigo-500 mt-1 mr-2"></i>
                                        <span>在Lovable中预览</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-globe text-indigo-500 mt-1 mr-2"></i>
                                        <span>通过Netlify连接自定义域名</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-mobile-alt text-indigo-500 mt-1 mr-2"></i>
                                        <span>添加PWA清单，使其像真正的应用一样可安装</span>
                                    </li>
                                </ul>
                            </div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                <i class="fas fa-rocket text-indigo-500 mr-1"></i>
                                完成后，应用即可上线，正常运行并准备好供用户使用。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 总结部分 -->
        <section class="mb-12 fade-in">
            <div class="max-w-4xl mx-auto">
                <div class="bg-gradient-to-r from-purple-500 to-pink-600 p-1 rounded-xl shadow-lg">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6">
                        <h3 class="text-2xl font-bold mb-4 text-center">构建出色产品的关键</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-6 text-center">
                            不需要大团队也能构建出色的产品，只需：
                        </p>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                                <i class="fas fa-tools text-purple-500 text-2xl mb-2"></i>
                                <p class="font-medium">正确的工具</p>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                                <i class="fas fa-comment-dots text-pink-500 text-2xl mb-2"></i>
                                <p class="font-medium">正确的提示</p>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                                <i class="fas fa-tasks text-blue-500 text-2xl mb-2"></i>
                                <p class="font-medium">专注的流程</p>
                            </div>
                        </div>
                        <p class="text-center text-lg font-medium">
                            这是在 @ignytlabs 构建MVP并快速交付的方式。
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white dark:bg-gray-800 shadow-inner mt-12 py-8">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="mb-6 md:mb-0">
                        <h3 class="text-lg font-bold mb-2">移动应用设计工作流</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            基于Prajwal Tomar的设计方法论
                        </p>
                    </div>
                    <div class="flex flex-col items-end">
                        <div class="flex space-x-4 mb-4">
                            <a href="https://github.com/caiwenhao" target="_blank" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                <i class="fab fa-github text-xl"></i>
                            </a>
                            <a href="https://twitter.com/caiwenhao" target="_blank" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                <i class="fab fa-twitter text-xl"></i>
                            </a>
                            <a href="https://linkedin.com/in/caiwenhao" target="_blank" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                <i class="fab fa-linkedin text-xl"></i>
                            </a>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            &copy; 2025 蔡文浩. 保留所有权利.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 深色/浅色模式切换
        const themeToggle = document.getElementById('theme-toggle');
        
        // 检查系统偏好
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        
        // 切换主题
        themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
        });
        
        // 添加滚动动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, { threshold: 0.1 });
        
        document.querySelectorAll('.step-card').forEach(card => {
            observer.observe(card);
        });
    </script>
</body>
</html>
