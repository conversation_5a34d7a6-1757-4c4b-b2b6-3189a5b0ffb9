<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web模板分析 - 框架与技术对比</title>
    <!-- TailwindCSS 通过CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        // 配置Tailwind主题
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                    },
                }
            }
        }
    </script>
    <style type="text/css">
        /* 自定义样式 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 加载动画 */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th {
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        /* 交替行颜色 */
        tr:nth-child(even) {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .dark tr:nth-child(even) {
            background-color: rgba(255, 255, 255, 0.05);
        }
        
        /* 框架标签样式 */
        .framework-tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.25rem;
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- 顶部导航栏 -->
    <header class="sticky top-0 z-50 bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fas fa-code text-primary-600 dark:text-primary-400 text-2xl"></i>
                <h1 class="text-xl font-bold">Web模板分析</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="index.html" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                    <i class="fas fa-home mr-1"></i> 首页
                </a>
                <!-- 深色/浅色模式切换按钮 -->
                <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-moon hidden dark:block text-yellow-300"></i>
                    <i class="fas fa-sun block dark:hidden text-yellow-500"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-4 py-8 fade-in">
        <!-- 介绍部分 -->
        <section class="mb-12">
            <h2 class="text-3xl font-bold mb-4 text-center">Web框架与模板分析</h2>
            <p class="text-lg text-gray-600 dark:text-gray-400 max-w-4xl mx-auto text-center mb-8">
                本页面提供了当前流行的Web开发框架、模板及其技术栈的详细分析，帮助开发者选择最适合项目需求的技术方案。
            </p>
        </section>

        <!-- 表格部分 -->
        <section class="mb-12">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <h3 class="text-2xl font-bold mb-4">模板对比表</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        下表展示了各种Web模板的核心框架、技术栈和主要特性的详细对比。
                    </p>
                    
                    <div class="table-container">
                        <table class="min-w-full">
                            <thead>
                                <tr class="bg-gray-100 dark:bg-gray-700">
                                    <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 border-b">模板名称</th>
                                    <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 border-b">主要框架/技术</th>
                                    <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 border-b">底层库/语言</th>
                                    <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 border-b">主要特性/目的</th>
                                    <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 border-b">可视化编辑</th>
                                    <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 border-b">集成/技术栈组件</th>
                                    <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700 dark:text-gray-300 border-b">类别</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Astro Platform Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Astro Platform Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">Astro</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        静态/SSR页面, 边缘函数, React组件
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Next.js Platform Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Next.js Platform Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-black text-white">Next.js</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        按需重新验证, 图片优化, 边缘路由, blobs 等
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        -
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Astro Supabase Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Astro Supabase Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">Astro</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        -
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        最小化启动器, Supabase集成, Tailwind
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200">Supabase</span>
                                        <span class="framework-tag bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200">Tailwind CSS</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Remix Admin Supabase Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Remix Admin Supabase Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">Remix</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        管理后台启动器, Supabase集成, Tailwind
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200">Supabase</span>
                                        <span class="framework-tag bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200">Tailwind CSS</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- TanStack Chat Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">TanStack Chat Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200">TanStack</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">(可能是 React/Vue等)</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        聊天启动器
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200">TanStack 库</span>
                                        <span class="framework-tag bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">Claude AI</span>
                                        <span class="framework-tag bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200">Tailwind CSS</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Content Ops Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Content Ops Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">未指定</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">未指定</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        内容运营(Content Ops)焦点, 可定制, 可视化编辑
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-check text-green-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">Netlify Create</span>
                                        <span class="framework-tag bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">Git 内容源</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Next.js + Contentful Minimal Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Next.js + Contentful Minimal Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-black text-white">Next.js</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        最小化启动器, Contentful 集成
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-check text-green-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Contentful</span>
                                        <span class="framework-tag bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">Netlify Create (隐含)</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Astro + Sanity Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Astro + Sanity Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">Astro</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        -
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        最小化启动器, Sanity 集成
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-check text-green-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">Sanity</span>
                                        <span class="framework-tag bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">Netlify Create (隐含)</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Portfolio site with visual editing -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Portfolio site with visual editing</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-black text-white">Next.js</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        作品集网站, 可视化编辑
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-check text-green-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200">Tailwind</span>
                                        <span class="framework-tag bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">Netlify Create</span>
                                        <span class="framework-tag bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">Git 内容源</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Bejamas Next.js Blog Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Bejamas Next.js Blog Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-black text-white">Next.js</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        博客启动器, 深色/浅色主题
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200">Tailwind</span>
                                        <span class="framework-tag bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">MDX</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Next.js + MUI Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Next.js + MUI Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-black text-white">Next.js</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        简单启动器, TypeScript, MUI, 可视化编辑
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-check text-green-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">MUI</span>
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">TypeScript</span>
                                        <span class="framework-tag bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">Netlify Create (隐含)</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Gatsby E-commerce Theme -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Gatsby E-commerce Theme</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">Gatsby</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        电商主题
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        -
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Next.js Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Next.js Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-black text-white">Next.js</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        一键式启动项目
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        -
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Nuxt Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Nuxt Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Nuxt</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Vue</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        登陆页模板
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200">Tailwind CSS</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Solid Quickstart -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Solid Quickstart</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Solid</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">SolidJS</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        基础骨架模板
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        -
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Hugo Blog -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Hugo Blog</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200">Hugo</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">Go 模板 (静态站点生成器)</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        博客网站, CMS 集成
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                        <span class="text-xs text-gray-500 ml-1">(但有CMS)</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">Decap CMS</span>
                                        <span class="framework-tag bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">Netlify Identity</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Angular Quickstart -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Angular Quickstart</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">Angular</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">TypeScript</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        基础骨架模板
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        -
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Hugo Quickstart -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Hugo Quickstart</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200">Hugo</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="text-gray-500 dark:text-gray-400 text-sm">Go 模板 (静态站点生成器)</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        基础骨架模板
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        -
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">热门</span>
                                    </td>
                                </tr>
                                
                                <!-- Astro Starlight Docs -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Astro Starlight Docs</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">Astro</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        -
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        文档网站
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">Starlight (Astro 文档主题)</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">合作伙伴</span>
                                    </td>
                                </tr>
                                
                                <!-- Next.js & Prisma Postgres Starter -->
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750">
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <div class="font-medium">Next.js & Prisma Postgres Starter</div>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-black text-white">Next.js</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">React</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 text-sm">
                                        博客应用, CRUD 操作
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <i class="fas fa-times text-red-500"></i>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Prisma</span>
                                        <span class="framework-tag bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Postgres (数据库)</span>
                                    </td>
                                    <td class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <span class="framework-tag bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">合作伙伴</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 分析部分 -->
        <section class="mb-12">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <h3 class="text-2xl font-bold mb-4">主要观察点</h3>
                    
                    <div class="space-y-6">
                        <div class="flex">
                            <div class="flex-shrink-0 mt-1">
                                <i class="fas fa-check-circle text-primary-500 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-lg font-semibold">元框架的主导地位</h4>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">
                                    Next.js (基于 React) 和 Astro 是此列表中最常出现的框架/元框架。
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex">
                            <div class="flex-shrink-0 mt-1">
                                <i class="fas fa-check-circle text-primary-500 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-lg font-semibold">React 生态系统</h4>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">
                                    许多热门选择（Next.js, Remix, Gatsby）都构建在 React 之上。
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex">
                            <div class="flex-shrink-0 mt-1">
                                <i class="fas fa-check-circle text-primary-500 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-lg font-semibold">Headless CMS 集成</h4>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">
                                    多个模板强调了与 CMS 平台（如 Contentful, Sanity, Decap CMS）的集成，并且常与可视化编辑功能（Netlify Create）相结合。
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex">
                            <div class="flex-shrink-0 mt-1">
                                <i class="fas fa-check-circle text-primary-500 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-lg font-semibold">样式</h4>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">
                                    Tailwind CSS 是跨不同框架的常用样式选择。MUI 也有出现。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white dark:bg-gray-800 shadow-inner mt-12 py-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <h3 class="text-lg font-semibold mb-2">作者信息</h3>
                    <p class="text-gray-600 dark:text-gray-400">蔡文浩</p>
                </div>
                <div class="flex space-x-4">
                    <a href="https://github.com/caiwenhao" target="_blank" rel="noopener noreferrer" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fab fa-github text-xl"></i>
                    </a>
                    <a href="https://twitter.com/caiwenhao" target="_blank" rel="noopener noreferrer" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fab fa-twitter text-xl"></i>
                    </a>
                    <a href="https://linkedin.com/in/caiwenhao" target="_blank" rel="noopener noreferrer" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fab fa-linkedin text-xl"></i>
                    </a>
                </div>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 text-center text-gray-500 dark:text-gray-400">
                <p> 2025 蔡文浩. 保留所有权利。</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 检测系统主题偏好并设置初始主题
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }

        // 主题切换功能
        document.getElementById('theme-toggle').addEventListener('click', function() {
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.theme = 'light';
            } else {
                document.documentElement.classList.add('dark');
                localStorage.theme = 'dark';
            }
        });
        
        // 添加淡入动画
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('section');
            sections.forEach(section => {
                section.classList.add('fade-in');
            });
        });
    </script>
</body>
</html>
