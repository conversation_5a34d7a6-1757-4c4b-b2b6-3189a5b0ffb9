<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>如何免费无限期使用Windsurf：终极指南</title>
    <!-- TailwindCSS 通过CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                },
            },
        }
    </script>
    <style>
        /* 自定义样式 */
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 内容淡入动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        
        /* 不同的延迟时间 */
        .delay-1 { animation-delay: 0.1s; }
        .delay-2 { animation-delay: 0.2s; }
        .delay-3 { animation-delay: 0.3s; }
        .delay-4 { animation-delay: 0.4s; }
        .delay-5 { animation-delay: 0.5s; }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-colors duration-300">
    <!-- 主题切换按钮 -->
    <button id="theme-toggle" class="fixed top-4 right-4 z-50 p-2 rounded-full bg-white dark:bg-gray-800 shadow-lg text-gray-800 dark:text-gray-200">
        <i class="fas fa-sun dark:hidden"></i>
        <i class="fas fa-moon hidden dark:block"></i>
    </button>

    <!-- 顶部导航 -->
    <nav class="sticky top-0 z-40 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md shadow-sm">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="#" class="flex items-center">
                        <i class="fas fa-wind text-primary-600 dark:text-primary-400 text-2xl mr-2"></i>
                        <span class="font-bold text-xl">Windsurf指南</span>
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#overview" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">概述</a>
                    <a href="#differences" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">免费与高级版</a>
                    <a href="#maximizing" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">最大化体验</a>
                    <a href="#best-practices" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">最佳实践</a>
                    <a href="#limitations" class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">局限性</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="hidden md:hidden bg-white dark:bg-gray-800 shadow-md">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#overview" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">概述</a>
                <a href="#differences" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">免费与高级版</a>
                <a href="#maximizing" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">最大化体验</a>
                <a href="#best-practices" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">最佳实践</a>
                <a href="#limitations" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">局限性</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 标题部分 -->
        <div class="text-center mb-16 opacity-0 fade-in">
            <h1 class="text-4xl md:text-5xl font-bold mb-4 text-gray-900 dark:text-white">如何免费无限期使用Windsurf</h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">终极指南：充分利用Windsurf免费版本的全部潜力</p>
        </div>

        <!-- 目录 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-10 opacity-0 fade-in delay-1">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <i class="fas fa-list-ul text-primary-600 dark:text-primary-400 mr-2"></i>目录
            </h2>
            <ul class="space-y-2">
                <li class="flex items-center">
                    <i class="fas fa-angle-right text-primary-600 dark:text-primary-400 mr-2"></i>
                    <a href="#overview" class="text-primary-600 dark:text-primary-400 hover:underline">Windsurf免费版概述</a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-angle-right text-primary-600 dark:text-primary-400 mr-2"></i>
                    <a href="#differences" class="text-primary-600 dark:text-primary-400 hover:underline">免费版与高级版的区别</a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-angle-right text-primary-600 dark:text-primary-400 mr-2"></i>
                    <a href="#maximizing" class="text-primary-600 dark:text-primary-400 hover:underline">最大化免费版体验</a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-angle-right text-primary-600 dark:text-primary-400 mr-2"></i>
                    <a href="#best-practices" class="text-primary-600 dark:text-primary-400 hover:underline">长期免费使用的最佳实践</a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-angle-right text-primary-600 dark:text-primary-400 mr-2"></i>
                    <a href="#user-feedback" class="text-primary-600 dark:text-primary-400 hover:underline">用户对免费版的评价</a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-angle-right text-primary-600 dark:text-primary-400 mr-2"></i>
                    <a href="#limitations" class="text-primary-600 dark:text-primary-400 hover:underline">需要注意的局限性</a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-angle-right text-primary-600 dark:text-primary-400 mr-2"></i>
                    <a href="#conclusion" class="text-primary-600 dark:text-primary-400 hover:underline">结论</a>
                </li>
            </ul>
        </div>

        <!-- 概述部分 -->
        <section id="overview" class="mb-16 opacity-0 fade-in delay-2">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div class="p-6 md:p-8">
                    <h2 class="text-2xl md:text-3xl font-bold mb-6 flex items-center">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mr-3"></i>
                        Windsurf免费版概述
                    </h2>
                    <p class="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                        Windsurf是一款基于VS Code构建的AI驱动代码编辑器，它提供了一个慷慨的免费版本，让用户可以访问其内置的Cascade Base模型。以下是您需要了解的内容：
                    </p>
                    
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 mb-6">
                        <h3 class="text-xl font-semibold mb-4 text-primary-700 dark:text-primary-300">无限访问权限</h3>
                        <p class="text-gray-700 dark:text-gray-300 mb-4">
                            免费版提供对Cascade Base模型（内部构建）的无限访问权限
                        </p>
                        
                        <h3 class="text-xl font-semibold mb-4 text-primary-700 dark:text-primary-300">免费功能包括：</h3>
                        <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>AI自动完成</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>编辑器内无限制AI聊天</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>AI驱动的命令指令</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>只读Cascade流程</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>零非许可数据训练</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>基本上下文感知和有限索引</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>归因过滤和传输加密</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>可选零日数据保留</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Windsurf标签页速度较慢（但仍然可用）</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 免费版与高级版的区别 -->
        <section id="differences" class="mb-16 opacity-0 fade-in delay-3">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div class="p-6 md:p-8">
                    <h2 class="text-2xl md:text-3xl font-bold mb-6 flex items-center">
                        <i class="fas fa-exchange-alt text-primary-600 dark:text-primary-400 mr-3"></i>
                        免费版与高级版的区别
                    </h2>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <!-- 免费版 -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 card-hover">
                            <div class="flex items-center mb-4">
                                <div class="bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                                    <i class="fas fa-gift text-primary-600 dark:text-primary-400 text-xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200">免费版</h3>
                            </div>
                            <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                    <span>无限访问Cascade Base模型</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                    <span>每月5个用户提示积分和5个流程操作积分（用于高级模型）</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                    <span>有限的索引能力</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                    <span>Windsurf标签页速度较慢</span>
                                </li>
                            </ul>
                        </div>
                        
                        <!-- 高级版 -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 card-hover">
                            <div class="flex items-center mb-4">
                                <div class="bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                                    <i class="fas fa-crown text-primary-600 dark:text-primary-400 text-xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200">高级版（专业版15美元/月，旗舰版60美元/月）</h3>
                            </div>
                            <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                    <span>访问高级模型（GPT-4o、Claude Sonnet等）</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                    <span>更多用户提示和流程操作积分</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                    <span>更快的处理速度</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                    <span>扩展的上下文长度</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                    <span>高级上下文感知</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                                    <span>增加索引限制</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 最大化免费版体验 -->
        <section id="maximizing" class="mb-16 opacity-0 fade-in delay-4">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div class="p-6 md:p-8">
                    <h2 class="text-2xl md:text-3xl font-bold mb-6 flex items-center">
                        <i class="fas fa-rocket text-primary-600 dark:text-primary-400 mr-3"></i>
                        最大化免费版体验
                    </h2>
                    
                    <!-- 掌握Cascade Base模型使用 -->
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold mb-4 flex items-center">
                            <span class="bg-primary-100 dark:bg-primary-900 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                                <span class="text-primary-600 dark:text-primary-400 font-bold">1</span>
                            </span>
                            掌握Cascade Base模型使用
                        </h3>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 ml-11">
                            <p class="text-gray-700 dark:text-gray-300 mb-4">
                                尽管是免费的，Cascade Base模型的能力令人惊讶。要最大化其潜力：
                            </p>
                            <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                                <li class="flex items-start">
                                    <i class="fas fa-lightbulb text-yellow-500 mt-1 mr-2"></i>
                                    <span><strong>提示要具体：</strong>指令越详细，Cascade Base就能提供越好的帮助</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-folder-open text-blue-500 mt-1 mr-2"></i>
                                    <span><strong>保持项目组织良好：</strong>较小、组织良好的代码库更容易被Base模型理解</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-clock text-purple-500 mt-1 mr-2"></i>
                                    <span><strong>使用较短的会话：</strong>将复杂任务分解为Base模型能够高效处理的较小块</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 实现Cascade Memory Bank -->
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold mb-4 flex items-center">
                            <span class="bg-primary-100 dark:bg-primary-900 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                                <span class="text-primary-600 dark:text-primary-400 font-bold">2</span>
                            </span>
                            实现Cascade Memory Bank
                        </h3>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 ml-11">
                            <p class="text-gray-700 dark:text-gray-300 mb-4">
                                一个名为<a href="https://github.com/GreatScottyMac/cascade-memory-bank" class="text-primary-600 dark:text-primary-400 hover:underline">Cascade Memory Bank</a>的社区开发GitHub仓库可以帮助Windsurf的AI在会话之间保持深度上下文：
                            </p>
                            <ol class="space-y-3 text-gray-700 dark:text-gray-300 list-decimal pl-5">
                                <li>将<code>.windsurfrules</code>文件复制到项目根目录</li>
                                <li>在Windsurf配置中设置<code>global_rules.md</code>文件</li>
                                <li>这有助于Cascade Base模型随着时间推移更好地维护项目上下文</li>
                            </ol>
                        </div>
                    </div>
                    
                    <!-- 优化工作流程 -->
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold mb-4 flex items-center">
                            <span class="bg-primary-100 dark:bg-primary-900 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                                <span class="text-primary-600 dark:text-primary-400 font-bold">3</span>
                            </span>
                            优化工作流程
                        </h3>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 ml-11">
                            <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                                <li class="flex items-start">
                                    <i class="fas fa-code-branch text-green-500 mt-1 mr-2"></i>
                                    <span><strong>经常提交：</strong>频繁进行git提交以保存进度并提供恢复点</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-comments text-blue-500 mt-1 mr-2"></i>
                                    <span><strong>高效使用聊天模式：</strong>有时聊天模式可以提供有价值的见解，而不消耗操作积分</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-file-code text-purple-500 mt-1 mr-2"></i>
                                    <span><strong>专注于单文件编辑：</strong>虽然Cascade可以处理多文件编辑，但Base模型在一次专注于一个文件时效果最佳</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 利用VS Code扩展 -->
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold mb-4 flex items-center">
                            <span class="bg-primary-100 dark:bg-primary-900 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                                <span class="text-primary-600 dark:text-primary-400 font-bold">4</span>
                            </span>
                            利用VS Code扩展
                        </h3>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 ml-11">
                            <p class="text-gray-700 dark:text-gray-300 mb-4">
                                由于Windsurf是基于VS Code构建的，您可以扩展其功能：
                            </p>
                            <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                                <li class="flex items-start">
                                    <i class="fas fa-puzzle-piece text-orange-500 mt-1 mr-2"></i>
                                    <span>安装提供专门语言支持的扩展</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-double text-green-500 mt-1 mr-2"></i>
                                    <span>使用代码检查和格式化扩展，减少对AI辅助的需求</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-code text-blue-500 mt-1 mr-2"></i>
                                    <span>实现代码片段和模板，减少对AI的依赖以处理常规代码</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 充分利用免费试用积分 -->
                    <div>
                        <h3 class="text-xl font-semibold mb-4 flex items-center">
                            <span class="bg-primary-100 dark:bg-primary-900 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                                <span class="text-primary-600 dark:text-primary-400 font-bold">5</span>
                            </span>
                            充分利用免费试用积分
                        </h3>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 ml-11">
                            <p class="text-gray-700 dark:text-gray-300 mb-4">
                                当您下载Windsurf时，您会收到一次性礼物：
                            </p>
                            <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                                <li class="flex items-start">
                                    <i class="fas fa-gift text-pink-500 mt-1 mr-2"></i>
                                    <span>50个高级模型用户提示积分</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-gift text-pink-500 mt-1 mr-2"></i>
                                    <span>200个高级模型流程操作积分</span>
                                </li>
                            </ul>
                            <p class="text-gray-700 dark:text-gray-300 mt-4">
                                明智地使用这些积分，用于Base模型可能难以处理的更复杂任务。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 长期免费使用的最佳实践 -->
        <section id="best-practices" class="mb-16 opacity-0 fade-in delay-5">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div class="p-6 md:p-8">
                    <h2 class="text-2xl md:text-3xl font-bold mb-6 flex items-center">
                        <i class="fas fa-star text-primary-600 dark:text-primary-400 mr-3"></i>
                        长期免费使用的最佳实践
                    </h2>
                    
                    <!-- 记录一切 -->
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold mb-4 flex items-center">
                            <span class="bg-primary-100 dark:bg-primary-900 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                                <span class="text-primary-600 dark:text-primary-400 font-bold">1</span>
                            </span>
                            记录一切
                        </h3>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 ml-11">
                            <p class="text-gray-700 dark:text-gray-300">
                                创建并维护关于代码库的详细文档，您可以在提示中引用这些文档。这有助于弥补Base模型的局限性。
                            </p>
                        </div>
                    </div>
                    
                    <!-- 实施有效的提示 -->
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold mb-4 flex items-center">
                            <span class="bg-primary-100 dark:bg-primary-900 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                                <span class="text-primary-600 dark:text-primary-400 font-bold">2</span>
                            </span>
                            实施有效的提示
                        </h3>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 ml-11">
                            <p class="text-gray-700 dark:text-gray-300 mb-4">
                                根据在线分享的开发者经验：
                            </p>
                            <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                                <li class="flex items-start">
                                    <i class="fas fa-list-ol text-blue-500 mt-1 mr-2"></i>
                                    <span>用明确的目标构建您的提示</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-level-up-alt text-green-500 mt-1 mr-2"></i>
                                    <span>从简单请求开始，然后逐步进行更复杂的请求</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-project-diagram text-purple-500 mt-1 mr-2"></i>
                                    <span>在需要时提供来自代码库的相关上下文</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 知道何时使用高级功能 -->
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold mb-4 flex items-center">
                            <span class="bg-primary-100 dark:bg-primary-900 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                                <span class="text-primary-600 dark:text-primary-400 font-bold">3</span>
                            </span>
                            知道何时使用高级功能
                        </h3>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 ml-11">
                            <p class="text-gray-700 dark:text-gray-300 mb-4">
                                如果您偶尔需要更高级的功能，请考虑：
                            </p>
                            <ul class="space-y-3 text-gray-700 dark:text-gray-300">
                                <li class="flex items-start">
                                    <i class="fas fa-shopping-cart text-orange-500 mt-1 mr-2"></i>
                                    <span>为特定复杂任务购买Flex积分，而不是订阅</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-chess-king text-yellow-500 mt-1 mr-2"></i>
                                    <span>战略性地使用每月5个免费高级积分</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 自动化重复任务 -->
                    <div>
                        <h3 class="text-xl font-semibold mb-4 flex items-center">
                            <span class="bg-primary-100 dark:bg-primary-900 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                                <span class="text-primary-600 dark:text-primary-400 font-bold">4</span>
                            </span>
                            自动化重复任务
                        </h3>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 ml-11">
                            <p class="text-gray-700 dark:text-gray-300">
                                为您经常执行的任务创建脚本，减少对AI辅助的依赖。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 用户对免费版的评价 -->
        <section id="user-feedback" class="mb-16 opacity-0 fade-in">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div class="p-6 md:p-8">
                    <h2 class="text-2xl md:text-3xl font-bold mb-6 flex items-center">
                        <i class="fas fa-comments text-primary-600 dark:text-primary-400 mr-3"></i>
                        用户对免费版的评价
                    </h2>
                    
                    <p class="text-gray-700 dark:text-gray-300 mb-6">
                        许多开发者报告说，Windsurf的免费版对日常编码任务确实有用：
                    </p>
                    
                    <div class="grid md:grid-cols-3 gap-6">
                        <!-- 评价1 -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 card-hover">
                            <div class="flex items-start mb-4">
                                <i class="fas fa-quote-left text-primary-400 text-2xl mr-3 mt-1"></i>
                                <p class="text-gray-700 dark:text-gray-300 italic">
                                    "Windsurf的免费版配合Cascade Base仍然在其强大功能方面超越了许多竞争对手"
                                </p>
                            </div>
                            <div class="flex items-center text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        
                        <!-- 评价2 -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 card-hover">
                            <div class="flex items-start mb-4">
                                <i class="fas fa-quote-left text-primary-400 text-2xl mr-3 mt-1"></i>
                                <p class="text-gray-700 dark:text-gray-300 italic">
                                    "与Cursor不同，Windsurf的免费版对其Cascade Base模型确实是无限制的"
                                </p>
                            </div>
                            <div class="flex items-center text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                        </div>
                        
                        <!-- 评价3 -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 card-hover">
                            <div class="flex items-start mb-4">
                                <i class="fas fa-quote-left text-primary-400 text-2xl mr-3 mt-1"></i>
                                <p class="text-gray-700 dark:text-gray-300 italic">
                                    "聊天中的上下文感知简直就像魔法一样"
                                </p>
                            </div>
                            <div class="flex items-center text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 需要注意的局限性 -->
        <section id="limitations" class="mb-16 opacity-0 fade-in">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div class="p-6 md:p-8">
                    <h2 class="text-2xl md:text-3xl font-bold mb-6 flex items-center">
                        <i class="fas fa-exclamation-triangle text-primary-600 dark:text-primary-400 mr-3"></i>
                        需要注意的局限性
                    </h2>
                    
                    <p class="text-gray-700 dark:text-gray-300 mb-6">
                        虽然免费版很慷慨，但用户报告了一些局限性：
                    </p>
                    
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-5 mb-6">
                        <ul class="space-y-4 text-gray-700 dark:text-gray-300">
                            <li class="flex items-start">
                                <i class="fas fa-minus-circle text-red-500 mt-1 mr-3"></i>
                                <span>Base模型不如GPT-4o和Claude Sonnet等高级模型强大</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-minus-circle text-red-500 mt-1 mr-3"></i>
                                <span>使用Base模型进行复杂的多文件编辑可能具有挑战性</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-minus-circle text-red-500 mt-1 mr-3"></i>
                                <span>写入模式功能与高级版相比更有限</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-minus-circle text-red-500 mt-1 mr-3"></i>
                                <span>Base模型可能在处理较新的框架或技术时遇到困难</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 结论 -->
        <section id="conclusion" class="mb-16 opacity-0 fade-in">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div class="p-6 md:p-8">
                    <h2 class="text-2xl md:text-3xl font-bold mb-6 flex items-center">
                        <i class="fas fa-flag-checkered text-primary-600 dark:text-primary-400 mr-3"></i>
                        结论
                    </h2>
                    
                    <p class="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                        Windsurf的无限免费版配合Cascade Base模型为希望获得AI辅助而不需要订阅的开发者提供了强大的工具。通过实施本指南中概述的策略，您可以有效地无限期免费使用Windsurf，在绝对必要时偶尔战略性地使用高级功能。
                    </p>
                    
                    <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                        Windsurf的真正价值在于其可访问性、创新性和对开发者的赋能承诺——即使是使用免费版的开发者。
                    </p>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white dark:bg-gray-800 shadow-md py-10">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <!-- 作者信息 -->
                <div class="mb-6 md:mb-0">
                    <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-gray-200">作者信息</h3>
                    <p class="text-gray-600 dark:text-gray-400">作者姓名: 文浩</p>
                    <div class="flex mt-3 space-x-4">
                        <a href="https://github.com/author" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="https://twitter.com/author" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="https://linkedin.com/in/author" class="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <!-- 版权信息 -->
                <div class="text-center md:text-right">
                    <p class="text-gray-600 dark:text-gray-400">
                        &copy; 2025 Windsurf指南。保留所有权利。
                    </p>
                    <p class="text-gray-500 dark:text-gray-500 text-sm mt-1">
                        本指南仅供教育目的，与Windsurf官方无关。
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 检查系统主题偏好并设置初始主题
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        
        // 主题切换功能
        document.getElementById('theme-toggle').addEventListener('click', function() {
            document.documentElement.classList.toggle('dark');
        });
        
        // 移动菜单切换
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });
        
        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
                
                // 如果移动菜单是打开的，点击后关闭它
                if (!document.getElementById('mobile-menu').classList.contains('hidden')) {
                    document.getElementById('mobile-menu').classList.add('hidden');
                }
            });
        });
        
        // 滚动时显示元素的动画
        function revealOnScroll() {
            const elements = document.querySelectorAll('.opacity-0');
            
            elements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;
                
                if (elementTop < window.innerHeight - elementVisible) {
                    element.classList.remove('opacity-0');
                }
            });
        }
        
        // 初始加载时执行一次
        revealOnScroll();
        
        // 滚动时执行
        window.addEventListener('scroll', revealOnScroll);
    </script>
</body>
</html>
