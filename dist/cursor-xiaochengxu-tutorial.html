<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用Cursor两小时开发微信小程序 - 完整教程</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --accent-color: #60a5fa;
            --text-primary: #1f2937;
            --text-secondary: #4b5563;
            --background-primary: #ffffff;
            --background-secondary: #f3f4f6;
            --card-bg: #ffffff;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
        }

        .dark {
            --primary-color: #60a5fa;
            --secondary-color: #93c5fd;
            --accent-color: #3b82f6;
            --text-primary: #f9fafb;
            --text-secondary: #e5e7eb;
            --background-primary: #111827;
            --background-secondary: #1f2937;
            --card-bg: #1f2937;
            --border-color: #374151;
            --success-color: #34d399;
            --warning-color: #fbbf24;
            --error-color: #f87171;
        }

        body {
            background-color: var(--background-primary);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .card {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .workflow-card {
            border-left: 4px solid var(--primary-color);
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
        }

        .step-number {
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .timeline-line {
            position: absolute;
            left: 16px;
            top: 40px;
            bottom: 0;
            width: 2px;
            background-color: var(--primary-color);
            z-index: -1;
        }
        
        .highlight {
            background-color: var(--accent-color);
            color: white;
            padding: 2px 4px;
            border-radius: 4px;
        }
        
        /* 添加一些微交互效果 */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .timeline-item:hover .step-number {
            transform: scale(1.1);
        }
        
        /* 响应式排版 */
        @media (max-width: 768px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
            
            .grid-cols-2 {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="theme-switch fixed top-4 right-4 z-50">
        <button id="themeToggle" class="p-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
            <i class="fas fa-moon dark:hidden"></i>
            <i class="fas fa-sun hidden dark:block"></i>
        </button>
    </div>

    <header class="py-10 text-center fade-in">
        <div class="container mx-auto px-4">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">用Cursor两小时开发微信小程序</h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 mb-6">从灵感到上线的完整工作流程</p>
            <div class="inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-4 py-2 rounded-full">
                <p>发布于: 2025-03-27</p>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8">
        <section class="mb-12 fade-in">
            <div class="max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card">
                <h2 class="text-2xl font-bold mb-4">简介</h2>
                <p class="mb-4">在AI技术飞速发展的今天，编程领域也迎来了重大变革。本文记录了作者通过亲身实践，仅用两小时就借助Cursor开发了一款查八字的微信小程序的过程，从需求分析到最终上线，全程记录并详细分享了开发过程中的每一个步骤和心得。</p>
                <div class="flex items-center">
                    <div class="mr-4">
                        <i class="fas fa-clock text-3xl text-blue-500"></i>
                    </div>
                    <div>
                        <h3 class="font-bold">开发时间</h3>
                        <p>仅需2小时，从零到完整小程序</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-center">开发流程概览</h2>
            <div class="relative max-w-4xl mx-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="card p-6 rounded-lg workflow-card fade-in" style="animation-delay: 100ms;">
                        <div class="flex items-center mb-4">
                            <div class="step-number mr-4">1</div>
                            <h3 class="text-xl font-bold">灵感搜集</h3>
                        </div>
                        <p>使用AI助手（如Grok 3）搜集小程序开发灵感，最终确定开发"查八字"的小程序。</p>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">用时：约2分钟</p>
                    </div>
                    
                    <div class="card p-6 rounded-lg workflow-card fade-in" style="animation-delay: 200ms;">
                        <div class="flex items-center mb-4">
                            <div class="step-number mr-4">2</div>
                            <h3 class="text-xl font-bold">需求分析</h3>
                        </div>
                        <p>借助AI生成详细的需求分析报告，明确目标用户、核心功能和使用场景。</p>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">输出：mrd.md文件</p>
                    </div>
                    
                    <div class="card p-6 rounded-lg workflow-card fade-in" style="animation-delay: 300ms;">
                        <div class="flex items-center mb-4">
                            <div class="step-number mr-4">3</div>
                            <h3 class="text-xl font-bold">产品PRD文档</h3>
                        </div>
                        <p>由AI产品经理输出专业的PRD文档，定义功能细节和用户界面要求。</p>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">输出：prd.md文件</p>
                    </div>
                    
                    <div class="card p-6 rounded-lg workflow-card fade-in" style="animation-delay: 400ms;">
                        <div class="flex items-center mb-4">
                            <div class="step-number mr-4">4</div>
                            <h3 class="text-xl font-bold">产品原型</h3>
                        </div>
                        <p>生成可视化的产品原型，设计精美UI界面和用户交互流程。</p>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">输出：prototype.html</p>
                    </div>
                    
                    <div class="card p-6 rounded-lg workflow-card fade-in" style="animation-delay: 500ms;">
                        <div class="flex items-center mb-4">
                            <div class="step-number mr-4">5</div>
                            <h3 class="text-xl font-bold">架构设计</h3>
                        </div>
                        <p>明确技术栈选择（前端uniapp、后端Java）并输出详细的系统架构设计文档。</p>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">输出：architecture.md</p>
                    </div>
                    
                    <div class="card p-6 rounded-lg workflow-card fade-in" style="animation-delay: 600ms;">
                        <div class="flex items-center mb-4">
                            <div class="step-number mr-4">6</div>
                            <h3 class="text-xl font-bold">开发阶段</h3>
                        </div>
                        <p>AI编码实现前后端功能，自动生成代码并展示开发过程。</p>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">用时：约15分钟</p>
                    </div>
                    
                    <div class="card p-6 rounded-lg workflow-card fade-in" style="animation-delay: 700ms;">
                        <div class="flex items-center mb-4">
                            <div class="step-number mr-4">7</div>
                            <h3 class="text-xl font-bold">后端功能验证</h3>
                        </div>
                        <p>在IDE中验证后端功能，解决依赖和API调用问题，确保核心计算逻辑正确。</p>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">用时：约40分钟</p>
                    </div>
                    
                    <div class="card p-6 rounded-lg workflow-card fade-in" style="animation-delay: 800ms;">
                        <div class="flex items-center mb-4">
                            <div class="step-number mr-4">8</div>
                            <h3 class="text-xl font-bold">接口联调</h3>
                        </div>
                        <p>确保前后端接口通信正常，验证整体数据流程正确性。</p>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">用时：约15分钟</p>
                    </div>
                    
                    <div class="card p-6 rounded-lg workflow-card fade-in" style="animation-delay: 900ms;">
                        <div class="flex items-center mb-4">
                            <div class="step-number mr-4">9</div>
                            <h3 class="text-xl font-bold">小程序调试</h3>
                        </div>
                        <p>使用微信开发者工具进行小程序本地调试，解决界面展示和功能问题。</p>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">用时：约15分钟</p>
                    </div>
                    
                    <div class="card p-6 rounded-lg workflow-card fade-in" style="animation-delay: 1000ms;">
                        <div class="flex items-center mb-4">
                            <div class="step-number mr-4">10</div>
                            <h3 class="text-xl font-bold">手机测试</h3>
                        </div>
                        <p>在真机环境测试小程序功能，解决域名验证和SSL证书问题。</p>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">用时：约20分钟</p>
                    </div>
                    
                    <div class="card p-6 rounded-lg workflow-card fade-in" style="animation-delay: 1100ms;">
                        <div class="flex items-center mb-4">
                            <div class="step-number mr-4">11</div>
                            <h3 class="text-xl font-bold">发布上线</h3>
                        </div>
                        <p>涉及备案、审核流程，准备上线小程序。</p>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">需要额外时间完成</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-center">详细工作流程</h2>
            <div class="max-w-4xl mx-auto">
                <!-- 详细步骤 -->
                <div class="space-y-8">
                    <!-- 步骤1：灵感搜集 -->
                    <div class="card p-6 rounded-lg fade-in">
                        <h3 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">01</span>
                            灵感搜集
                        </h3>
                        <div class="pl-4 border-l-4 border-blue-500">
                            <p class="mb-3">开发小程序的第一步是寻找灵感。作者通过向AI助手Grok 3提问，探索适合开发的小程序功能。</p>
                            <p class="mb-3">最终确定开发"查八字"的小程序，可以根据用户的阳历出生日期及出生时辰，自动计算农历日期、天干地支、所属五行、五行缺失情况和生肖等信息。</p>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mt-3">
                                <p class="font-medium">💡 小贴士：</p>
                                <p>用时仅2分钟，通过AI快速获取创意是提高效率的好方法。</p>
                            </div>
                            <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg border-l-4 border-yellow-500">
                                <p class="font-medium text-yellow-800 dark:text-yellow-200">⌨️ Prompt示例：</p>
                                <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                                    推荐几个适合新手开发的微信小程序创意，要求功能相对简单但实用，易于实现且有一定的用户需求
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤2：需求分析 -->
                    <div class="card p-6 rounded-lg fade-in">
                        <h3 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">02</span>
                            需求分析
                        </h3>
                        <div class="pl-4 border-l-4 border-blue-500">
                            <p class="mb-3">有了灵感后，需要进行深入的需求分析，确定产品价值和可行性。作者让AI扮演产品经理角色，生成了详细的需求分析报告。</p>
                            <p class="mb-3">提示词包含了对产品概述、用户需求分析、功能需求、关键业务流程、开发周期和商业模式等方面的要求，最终生成了完整的需求分析文档。</p>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mt-3">
                                <p class="font-medium">📝 输出文件：</p>
                                <p>mrd.md - 详细的需求分析报告</p>
                            </div>
                            <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg border-l-4 border-yellow-500">
                                <p class="font-medium text-yellow-800 dark:text-yellow-200">⌨️ Prompt示例：</p>
                                <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                                    我想开发一款微信小程序，主要功能是：<br><br>
                                    • 用户输入阳历出生日期和出生时辰，系统自动计算：<br>
                                    • 对应的农历日期<br>
                                    • 天干地支<br>
                                    • 所属五行<br>
                                    • 五行缺失情况<br>
                                    • 所属生肖<br>
                                    • 系统调用 DeepSeek API 进行推理，生成相关信息。<br><br>
                                    请你作为产品经理，对该小程序进行需求分析，并输出一份<b>详细的需求分析报告</b>，包括但不限于：<br><br>
                                    1.<b>产品概述</b>（介绍产品的核心功能和目标用户）<br>
                                    2.<b>用户需求分析</b>（目标用户群体、使用场景、核心需求）<br>
                                    3.<b>功能需求</b><br>
                                    4.<b>关键业务流程</b>（示例：用户输入数据 -> API 计算 -> 结果展示）<br>
                                    5.<b>开发周期</b>（建议的开发计划、MVP 版本优先级）<br>
                                    6.<b>商业模式</b>（是否有付费功能、盈利方式）<br><br>
                                    帮我将需求分析报告写在 mrd.md 里面。
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤3：产品PRD文档 -->
                    <div class="card p-6 rounded-lg fade-in">
                        <h3 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">03</span>
                            产品PRD文档
                        </h3>
                        <div class="pl-4 border-l-4 border-blue-500">
                            <p class="mb-3">基于需求分析，作者请AI按照最小MVP版本编写了专业的产品需求(PRD)文档，并提供了参考小程序设计图作为指导。</p>
                            <p class="mb-3">仅用不到2分钟，AI就生成了一份约2000字的PRD文档，详细定义了产品功能和界面要求。</p>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mt-3">
                                <p class="font-medium">📋 关键点：</p>
                                <p>在这一阶段，如发现与设计初衷不符的内容，应及时调整，避免后期返工。</p>
                            </div>
                            <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg border-l-4 border-yellow-500">
                                <p class="font-medium text-yellow-800 dark:text-yellow-200">⌨️ Prompt示例：</p>
                                <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                                    接下来根据需求分析，按照最小 MVP 的版本，帮我写一份专业的产品需求（PRD）文档，我会给你三张参考小程序设计图，请参考我给你的图片帮我完成需求文档。并输出到 prd.md 中。
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤4：产品原型 -->
                    <div class="card p-6 rounded-lg fade-in">
                        <h3 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">04</span>
                            产品原型
                        </h3>
                        <div class="pl-4 border-l-4 border-blue-500">
                            <p class="mb-3">有了PRD文档，下一步是设计产品原型。作者使用专门的提示词，让AI扮演全栈工程师兼产品设计师的角色，生成了完整的小程序原型图。</p>
                            <p class="mb-3">作者通过多次调整提示词，指导AI优化背景图、移除不必要的元素，并调整布局，最终得到了满意的原型设计。</p>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mt-3">
                                <p class="font-medium">⏱️ 耗时：</p>
                                <p>原型设计及调整共花费约5分钟。</p>
                            </div>
                            <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg border-l-4 border-yellow-500">
                                <p class="font-medium text-yellow-800 dark:text-yellow-200">⌨️ Prompt示例：</p>
                                <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                                    你是一位全栈工程师，同时精通产品规划和 UI 设计。<br><br>
                                    请根据产品 PRD 文档帮我输出完整的小程序原型图，请通过以下方式帮我完成小程序所有原型图片的设计。<br><br>
                                    1、按照 PRD 文档要求以及我给你的图片参考来设计最小 MVP 版本<br>
                                    2、以产品经理的视角结合 PRD 文档去设计页面和交互；<br>
                                    3、作为设计师思考这些原型界面的设计，并以设计师的视角去输出完整的 UI/UX；<br>
                                    4、使用 html 在一个界面上生成所有的原型界面，文件命名为: prototype.html，可以使用 FontAwesome 等开源图标库，让原型显得更精美和接近真实<br>
                                    5、我希望这些界面是需要能直接拿去进行开发的
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤5：架构设计 -->
                    <div class="card p-6 rounded-lg fade-in">
                        <h3 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">05</span>
                            架构设计
                        </h3>
                        <div class="pl-4 border-l-4 border-blue-500">
                            <p class="mb-3">基于原型和PRD文档，作者指定了开发规范和技术选型，让AI输出了架构设计文档。</p>
                            <p class="mb-3">前端采用uniapp框架，后端使用Java开发，并遵循阿里巴巴开发编码规范和REST风格接口公约。后端主要提供计算接口，通过调用DeepSeek API获取信息，转换为对应的接口字段返回给前端。</p>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mt-3">
                                <p class="font-medium">📐 架构文档内容包括：</p>
                                <ul class="list-disc list-inside">
                                    <li>系统架构图</li>
                                    <li>技术选型</li>
                                    <li>前端项目结构</li>
                                    <li>页面组件设计</li>
                                    <li>后端项目结构</li>
                                    <li>接口设计</li>
                                </ul>
                            </div>
                            <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg border-l-4 border-yellow-500">
                                <p class="font-medium text-yellow-800 dark:text-yellow-200">⌨️ Prompt示例：</p>
                                <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                                    请依据 prototype.html、prd.md 来进行微信小程序的整体架构设计，注意我希望整体前端使用的是 uniapp，后端使用的 java 来开。<br><br>
                                    1、整体满足微信小程序的开发规范。<br><br>
                                    2、前端按照原型设计来开发，后端我希望满足阿里巴巴开发编码规范，遵守 rest 风格的接口公约，且后端主要提供一个计算的接口，返回前端需要的参数，<br><br>
                                    3、后端具体计算的逻辑，我希望是能直接通过调用 DeepSeek 的 API 来获得信息，然后转换为对应的接口字段，最终返回给前端。<br><br>
                                    请帮我输出架构设计文档到 architecture.md
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤6：开发阶段 -->
                    <div class="card p-6 rounded-lg fade-in">
                        <h3 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">06</span>
                            开发阶段
                        </h3>
                        <div class="pl-4 border-l-4 border-blue-500">
                            <p class="mb-3">进入正式开发阶段，作者使用简洁而信息丰富的提示词，指导AI根据架构设计文档、需求文档和原型图进行代码开发。</p>
                            <p class="mb-3">作者选择Claude 3.7的Agent模式进行开发，AI自动创建了前后端的项目结构和代码文件。整个过程中，作者只需点击"继续"按钮，即可看到AI逐步完成代码编写。</p>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mt-3">
                                <p class="font-medium">⌨️ 开发细节：</p>
                                <p>整个开发过程预计持续约15分钟，作者只需点击鼠标即可完成，无需手动编写代码。</p>
                            </div>
                            <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg border-l-4 border-yellow-500">
                                <p class="font-medium text-yellow-800 dark:text-yellow-200">⌨️ Prompt示例：</p>
                                <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                                    @architecture.md 请帮我根据架构设计文档、需求文档以及原型图进行代码的开发，请注意，前后端项目结构是分离的，严格按照架构设计文档来开发。
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤7：后端功能验证 -->
                    <div class="card p-6 rounded-lg fade-in">
                        <h3 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">07</span>
                            后端功能验证
                        </h3>
                        <div class="pl-4 border-l-4 border-blue-500">
                            <p class="mb-3">代码生成后，需要验证其功能。作者在IDE中打开后端代码并尝试启动，遇到了一些依赖和配置问题。</p>
                            <p class="mb-3">作者回到Cursor让AI修复这些问题。原计划使用DeepSeek API，但由于额度用完，改为使用豆包lite模型，这也提高了响应速度。</p>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mt-3">
                                <p class="font-medium">⚠️ 注意事项：</p>
                                <p>AI一次性生成的代码可能存在问题，需要进行调试和修改。后端功能验证阶段花费了约40分钟。</p>
                            </div>
                            <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg border-l-4 border-yellow-500">
                                <p class="font-medium text-yellow-800 dark:text-yellow-200">⌨️ Prompt示例：</p>
                                <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                                    请帮我修复后端代码中的问题，确保后端功能正常运行。
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤8：接口联调 -->
                    <div class="card p-6 rounded-lg fade-in">
                        <h3 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">08</span>
                            接口联调
                        </h3>
                        <div class="pl-4 border-l-4 border-blue-500">
                            <p class="mb-3">后端验证完成后，进行接口联调，确保前端能够正确调用后端接口并处理返回数据。</p>
                            <p class="mb-3">作者在Cursor中进行联调，并根据需要在IDE中查看和调整代码。这一过程花费约15分钟。</p>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mt-3">
                                <p class="font-medium">🔄 联调重点：</p>
                                <p>确保前后端数据格式一致，接口调用正确，错误处理完善。</p>
                            </div>
                            <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg border-l-4 border-yellow-500">
                                <p class="font-medium text-yellow-800 dark:text-yellow-200">⌨️ Prompt示例：</p>
                                <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                                    请帮我继续完善前端代码，要求与后端能顺利的通信
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤9：小程序调试 -->
                    <div class="card p-6 rounded-lg fade-in">
                        <h3 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">09</span>
                            小程序调试
                        </h3>
                        <div class="pl-4 border-l-4 border-blue-500">
                            <p class="mb-3">使用微信开发者工具进行小程序本地调试。作者导入项目，并进行功能测试，发现了一些问题（如界面渲染异常、域名校验错误等）。</p>
                            <p class="mb-3">通过向Cursor提供问题描述和截图，AI能够快速提供修复方案。UI展示问题（如结果靠右边太多）也通过类似方式解决。</p>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mt-3">
                                <p class="font-medium">🛠️ 调试技巧：</p>
                                <p>在微信开发者工具中可以关闭域名校验，方便本地调试；通过查看网络请求可以验证接口调用是否正常。</p>
                            </div>
                            <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg border-l-4 border-yellow-500">
                                <p class="font-medium text-yellow-800 dark:text-yellow-200">⌨️ Prompt示例：</p>
                                <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                                    请帮我修复小程序调试中的问题，确保小程序功能正常运行。
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤10：手机测试 -->
                    <div class="card p-6 rounded-lg fade-in">
                        <h3 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">10</span>
                            手机测试
                        </h3>
                        <div class="pl-4 border-l-4 border-blue-500">
                            <p class="mb-3">在开发工具测试完毕后，需要在真机上进行测试。这一步涉及到域名验证、HTTPS证书等问题。</p>
                            <p class="mb-3">作者将后端服务部署到服务器上，修改前端配置连接线上环境，并解决了跨域和HTTPS的问题。</p>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mt-3">
                                <p class="font-medium">📱 要点提示：</p>
                                <p>要想直接预览小程序，需要配置HTTPS证书，且在微信小程序后台配置合法域名（需要配置子域名，不能配置顶级域名）。</p>
                            </div>
                            <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg border-l-4 border-yellow-500">
                                <p class="font-medium text-yellow-800 dark:text-yellow-200">⌨️ Prompt示例：</p>
                                <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                                    我现在已经将后端服务部署在了我的服务器，域名是XXX，端口号改为了XXX，请帮我前端改下配置，让他连接线上环境.要求本地环境和线上环境区分开来
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤11：发布上线 -->
                    <div class="card p-6 rounded-lg fade-in">
                        <h3 class="text-xl font-bold mb-4 text-blue-600 dark:text-blue-400">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded mr-2">11</span>
                            发布上线
                        </h3>
                        <div class="pl-4 border-l-4 border-blue-500">
                            <p class="mb-3">最后一步是发布上线。根据工信部规定，未上架的微信小程序必须在提交上线审核前完成备案。</p>
                            <p class="mb-3">这一步骤相对复杂，涉及域名申请、备案、审核等流程，特别是对于小白用户来说可能存在一定门槛。</p>
                            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg mt-3">
                                <p class="font-medium">🏁 总结：</p>
                                <p>整个开发过程仅用2小时完成，作者在不写一行代码的情况下，通过Cursor和AI工具完成了从创意到上线准备的全过程。</p>
                            </div>
                            <div class="mt-4 bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg border-l-4 border-yellow-500">
                                <p class="font-medium text-yellow-800 dark:text-yellow-200">⌨️ Prompt示例：</p>
                                <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                                    请帮我完成发布上线的流程，包括备案、审核等步骤。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-center">实用Prompt技巧总结</h2>
            <div class="max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card">
                <div class="space-y-4">
                    <h3 class="text-xl font-bold">关键提示词技巧</h3>
                    <ol class="space-y-4">
                        <li class="p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                            <p class="font-bold">1. 角色定位明确</p>
                            <p class="text-gray-700 dark:text-gray-300">通过指定AI扮演的角色（如产品经理、全栈工程师、UI设计师），能够获得更专业的输出。例如："你是一位全栈工程师，同时精通产品规划和UI设计"。</p>
                        </li>
                        <li class="p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                            <p class="font-bold">2. 详细的输出要求</p>
                            <p class="text-gray-700 dark:text-gray-300">明确指定AI输出的内容、格式和文件名，例如："帮我将需求分析报告写在mrd.md里面"或"请帮我输出架构设计文档到architecture.md"。</p>
                        </li>
                        <li class="p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                            <p class="font-bold">3. 技术栈明确指定</p>
                            <p class="text-gray-700 dark:text-gray-300">在架构设计阶段明确技术选型，例如："前端使用uniapp，后端使用java"，这样AI生成的代码更符合预期。</p>
                        </li>
                        <li class="p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                            <p class="font-bold">4. 参考资料引用</p>
                            <p class="text-gray-700 dark:text-gray-300">通过@文件名语法引用已有文档，例如："@architecture.md 请帮我根据架构设计文档..."，让AI能够基于已有文档进行创作。</p>
                        </li>
                        <li class="p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                            <p class="font-bold">5. 问题精准描述</p>
                            <p class="text-gray-700 dark:text-gray-300">在调试阶段，通过精确描述问题（如"结果太靠右边了"）和提供截图，可以得到更精准的修复方案。</p>
                        </li>
                    </ol>
                    
                    <div class="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
                        <p class="font-bold text-center">通用Prompt模板</p>
                        <div class="bg-white dark:bg-gray-800 p-3 rounded mt-2 text-sm font-mono overflow-x-auto">
                            作为[角色]，请帮我[具体任务]。<br><br>
                            要求如下：<br>
                            1. [要求1]<br>
                            2. [要求2]<br>
                            3. [要求3]<br><br>
                            请参考[参考资料/文档]，并输出结果到[指定文件]。
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <h3 class="text-xl font-bold mb-4">开发效率提升技巧</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                                <p class="font-bold text-green-800 dark:text-green-200">📈 渐进式开发</p>
                                <p>从需求分析→架构设计→代码实现的渐进式开发流程，让AI更好理解项目全貌。</p>
                            </div>
                            <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                                <p class="font-bold text-green-800 dark:text-green-200">📋 任务分解</p>
                                <p>将复杂任务分解为小步骤，一次专注解决一个问题，例如先解决后端问题再处理前端。</p>
                            </div>
                            <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                                <p class="font-bold text-green-800 dark:text-green-200">📋 文档驱动</p>
                                <p>让AI先输出文档（PRD、架构设计），再基于文档生成代码，保证一致性和完整性。</p>
                            </div>
                            <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                                <p class="font-bold text-green-800 dark:text-green-200">🛠️ 迭代优化</p>
                                <p>发现问题立即纠正，给予AI明确反馈，帮助它理解和改进输出质量。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                        <p class="font-bold">Cursor开发经验总结</p>
                        <ul class="list-disc list-inside mt-2 space-y-2">
                            <li>前端开发：Cursor生成的前端代码相对可靠，UI和基本功能实现较好</li>
                            <li>后端开发：复杂逻辑可能需要更多人工指导和调整</li>
                            <li>调试阶段：需要针对性地提供反馈，帮助AI定位和解决问题</li>
                            <li>时间分配：编写代码约占30%时间，调试和修复约占70%时间</li>
                            <li>文档管理：保持文档（mrd、prd、架构文档）的一致性和完整性，有助于后续开发</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section class="mb-12">
            <h2 class="text-2xl font-bold mb-6 text-center">开发总结与思考</h2>
            <div class="max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card">
                <div class="space-y-4">
                    <h3 class="text-xl font-bold">惊喜与不足</h3>
                    <div class="pl-4 border-l-4 border-green-500 mb-4">
                        <p class="text-green-700 dark:text-green-400 font-medium">惊喜：</p>
                        <p>无需编写代码，仅用2小时就完成了微信小程序的开发，极大提高了开发效率。</p>
                    </div>
                    <div class="pl-4 border-l-4 border-yellow-500">
                        <p class="text-yellow-700 dark:text-yellow-400 font-medium">不足：</p>
                        <p>AI生成的前端代码质量尚可，但后端代码存在一些问题，需要人工调整。真正的开发过程中，写代码只占用了约半小时，更多时间用于解决AI生成代码的bug。</p>
                    </div>
                    
                    <div class="mt-6">
                        <h3 class="text-xl font-bold mb-2">结论</h3>
                        <p>目前的AI编程水平大致相当于实习生水平，但已经足够惊艳。人人都能开发应用的时代正在到来，AI辅助编程将会大幅降低软件开发的门槛。</p>
                    </div>
                    
                    <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                        <p class="text-center font-bold">整个开发时间分配</p>
                        <div class="grid grid-cols-2 gap-4 mt-3">
                            <div>
                                <p class="font-medium">设计阶段：30分钟</p>
                                <p>需求分析、PRD文档、产品原型、架构设计</p>
                            </div>
                            <div>
                                <p class="font-medium">开发与调试：90分钟</p>
                                <ul class="list-disc list-inside">
                                    <li>代码生成：约15分钟</li>
                                    <li>后端验证：约40分钟</li>
                                    <li>接口联调：约15分钟</li>
                                    <li>小程序调试：约15分钟</li>
                                    <li>手机测试：约20分钟</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="bg-gray-100 dark:bg-gray-800 py-8">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="flex flex-col md:flex-row justify-between items-center mb-6">
                    <div class="text-center md:text-left mb-4 md:mb-0">
                        <h3 class="text-xl font-bold">作者信息</h3>
                        <p class="text-gray-600 dark:text-gray-400">作者: 苍何</p>
                        <p class="text-gray-600 dark:text-gray-400">微信公众号: 苍何</p>
                    </div>
                    <div class="flex space-x-4">
                        <a href="https://github.com/canghe" target="_blank" class="text-gray-700 dark:text-gray-300 hover:text-blue-500 transition-colors duration-300">
                            <i class="fab fa-github text-2xl"></i>
                        </a>
                        <a href="https://twitter.com/canghe" target="_blank" class="text-gray-700 dark:text-gray-300 hover:text-blue-500 transition-colors duration-300">
                            <i class="fab fa-twitter text-2xl"></i>
                        </a>
                        <a href="https://linkedin.com/in/canghe" target="_blank" class="text-gray-700 dark:text-gray-300 hover:text-blue-500 transition-colors duration-300">
                            <i class="fab fa-linkedin text-2xl"></i>
                        </a>
                    </div>
                </div>
                <div class="text-center border-t border-gray-200 dark:border-gray-700 pt-6">
                    <p class="text-gray-600 dark:text-gray-400">原文发布于: 人人都是产品经理</p>
                    <p class="text-gray-600 dark:text-gray-400">题图来自Unsplash，基于CC0协议</p>
                    <p class="text-gray-600 dark:text-gray-400 mt-2">&copy; 2025 版权所有</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 深色模式切换功能
        const themeToggle = document.getElementById('themeToggle');
        const html = document.documentElement;
        
        // 检查用户系统偏好
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            html.classList.add('dark');
        }
        
        // 点击切换主题
        themeToggle.addEventListener('click', () => {
            html.classList.toggle('dark');
        });
        
        // 添加滚动动画
        const fadeElements = document.querySelectorAll('.fade-in');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = 1;
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });
        
        fadeElements.forEach(element => {
            element.style.opacity = 0;
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            observer.observe(element);
        });
    </script>
</body>
</html>
